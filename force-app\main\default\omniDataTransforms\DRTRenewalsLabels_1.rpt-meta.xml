<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;totaleTitoli&quot; : {
    &quot;numero&quot; : 1050,
    &quot;premio&quot; : 1542300.5
  },
  &quot;titoliIncassati&quot; : {
    &quot;numero&quot; : 800,
    &quot;premio&quot; : 1200000
  },
  &quot;titoliNonIncassati&quot; : {
    &quot;numero&quot; : 250,
    &quot;premio&quot; : 342300.5
  }
}</expectedInputJson>
    <expectedOutputJson>{
  &quot;totaleTitoli&quot; : {
    &quot;numero&quot; : 1050,
    &quot;premio&quot; : 1542300.5,
    &quot;label&quot; : &quot;Totale&quot;
  },
  &quot;titoliIncassati&quot; : {
    &quot;numero&quot; : 800,
    &quot;premio&quot; : 1200000,
    &quot;label&quot; : &quot;Incassati&quot;
  },
  &quot;titoliNonIncassati&quot; : {
    &quot;numero&quot; : 250,
    &quot;premio&quot; : 342300.5,
    &quot;label&quot; : &quot;Da Incassare&quot;
  }
}</expectedOutputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>DRTRenewalsLabels</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <defaultValue>Incassati</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DRTRenewalsLabelsCustom5807</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsLabels</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>titoliIncassati:label</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>Non Incassati</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DRTRenewalsLabelsCustom9533</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsLabels</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>titoliNonIncassati:label</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DRTRenewalsLabelsCustom175</globalKey>
        <inputFieldName>totaleTitoli:premio</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsLabels</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Double</outputFieldFormat>
        <outputFieldName>totaleTitoli:premio</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>0</defaultValue>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DRTRenewalsLabelsCustom3661</globalKey>
        <inputFieldName>titoliIncassati:numero</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsLabels</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Double</outputFieldFormat>
        <outputFieldName>titoliIncassati:numero</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>0</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DRTRenewalsLabelsCustom659</globalKey>
        <inputFieldName>titoliNonIncassati:premio</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsLabels</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Double</outputFieldFormat>
        <outputFieldName>titoliNonIncassati:premio</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>Totale</defaultValue>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DRTRenewalsLabelsCustom6527</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsLabels</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>totaleTitoli:label</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>0</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DRTRenewalsLabelsCustom6125</globalKey>
        <inputFieldName>titoliNonIncassati:numero</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsLabels</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Double</outputFieldFormat>
        <outputFieldName>titoliNonIncassati:numero</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>0</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DRTRenewalsLabelsCustom349</globalKey>
        <inputFieldName>titoliIncassati:premio</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsLabels</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Double</outputFieldFormat>
        <outputFieldName>titoliIncassati:premio</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>0</defaultValue>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DRTRenewalsLabelsCustom3534</globalKey>
        <inputFieldName>totaleTitoli:numero</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsLabels</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Double</outputFieldFormat>
        <outputFieldName>totaleTitoli:numero</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;totaleTitoli&quot; : {
    &quot;numero&quot; : 1050,
    &quot;premio&quot; : 1542300.5
  },
  &quot;titoliIncassati&quot; : {
    &quot;numero&quot; : 800,
    &quot;premio&quot; : 1200000
  },
  &quot;titoliNonIncassati&quot; : {
    &quot;numero&quot; : 250,
    &quot;premio&quot; : 342300.5
  }
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Transform</type>
    <uniqueName>DRTRenewalsLabels_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
