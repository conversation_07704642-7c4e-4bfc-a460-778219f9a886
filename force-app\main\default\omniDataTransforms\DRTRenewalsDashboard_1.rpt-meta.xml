<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;bdy&quot; : {
    &quot;totaleTitoli&quot; : {
      &quot;numero&quot; : 1050,
      &quot;premio&quot; : 1542300.5
    },
    &quot;titoliIncassati&quot; : {
      &quot;numero&quot; : 800,
      &quot;premio&quot; : 1200000
    },
    &quot;titoliNonIncassati&quot; : {
      &quot;numero&quot; : 250,
      &quot;premio&quot; : 342300.5
    }
  },
  &quot;hdr&quot; : {
    &quot;typ&quot; : 0
  }
}</expectedInputJson>
    <fieldLevelSecurityEnabled>true</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>DRTRenewalsDashboard</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>4426c2e9-eb7c-4d4c-9511-64c5bb002890</globalKey>
        <inputFieldName>dataList:label</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsDashboard</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>label</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;test&quot; : &quot;test1&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:titoliIncassati var:titoliNonIncassati var:totaleTitoli LIST</formulaConverted>
        <formulaExpression>LIST(titoliIncassati,titoliNonIncassati,totaleTitoli)</formulaExpression>
        <formulaResultPath>dataList</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>DRTRenewalsDashboardCustom3417</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsDashboard</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>0</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DRTRenewalsDashboardCustom7612</globalKey>
        <inputFieldName>dataList:numero</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsDashboard</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Double</outputFieldFormat>
        <outputFieldName>numero</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>0</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>81b5885a-6a6e-4f74-9c19-051459e04076</globalKey>
        <inputFieldName>dataList:premio</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsDashboard</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Double</outputFieldFormat>
        <outputFieldName>premio</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;totaleTitoli&quot; : {
    &quot;label&quot; : &quot;Totale&quot;,
    &quot;premio&quot; : 1542300.5,
    &quot;numero&quot; : 1050
  },
  &quot;titoliIncassati&quot; : {
    &quot;label&quot; : &quot;Incassati&quot;,
    &quot;numero&quot; : 800,
    &quot;premio&quot; : 1200000
  },
  &quot;titoliNonIncassati&quot; : {
    &quot;label&quot; : &quot;Non Incassati&quot;,
    &quot;premio&quot; : 342300.5,
    &quot;numero&quot; : 250
  }
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Transform</type>
    <uniqueName>DRTRenewalsDashboard_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
