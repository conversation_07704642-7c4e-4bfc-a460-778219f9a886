<?xml version="1.0" encoding="UTF-8"?>
<OmniIntegrationProcedure xmlns="http://soap.sforce.com/2006/04/metadata">
    <customJavaScript>{
    &quot;AccountId&quot;: &quot;0019O000010SswEQAS&quot;,
    &quot;UserId&quot;: &quot;0059O00000WEYgvQAH&quot;,
    &quot;firstThreeRowsOnly&quot;: &quot;false&quot;,
    &quot;isAbbinato&quot;: &quot;false&quot;
}</customJavaScript>
    <description>New version with UniSalute response added</description>
    <elementTypeComponentMapping>{&quot;ElementTypeToHTMLTemplateList&quot;:[]}</elementTypeComponentMapping>
    <isActive>false</isActive>
    <isIntegProcdSignatureAvl>false</isIntegProcdSignatureAvl>
    <isIntegrationProcedure>true</isIntegrationProcedure>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <isMetadataCacheDisabled>false</isMetadataCacheDisabled>
    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
    <isTestProcedure>false</isTestProcedure>
    <isWebCompEnabled>false</isWebCompEnabled>
    <language>Procedure</language>
    <name>UniDS_TitoliInScadenza</name>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>CheckCustomPermissions</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;additionalInput&quot; : {
    &quot;customPermissions&quot; : [ &quot;X169_506000000&quot;, &quot;X172_201030200&quot;, &quot;X172_201030300&quot;, &quot;X172_201030400&quot;, &quot;X172_201030500&quot;, &quot;X172_201030600&quot;, &quot;X172_201030700&quot;, &quot;X172_201030800&quot;, &quot;X172_201030900&quot;, &quot;X172_201031000&quot;, &quot;X172_202020000&quot;, &quot;X172_202020100&quot;, &quot;X172_202020101&quot;, &quot;X172_202020102&quot;, &quot;X172_202020103&quot;, &quot;X172_202020104&quot;, &quot;X172_202020105&quot;, &quot;X172_202020500&quot;, &quot;X172_202020501&quot;, &quot;X172_202020502&quot;, &quot;X172_202020503&quot;, &quot;X172_202020504&quot;, &quot;X172_202150504&quot;, &quot;X172_202150600&quot;, &quot;X172_202020600&quot;, &quot;X172_202150000&quot;, &quot;X172_202150101&quot;, &quot;X172_202150102&quot;, &quot;X172_202150100&quot;, &quot;X172_202150103&quot;, &quot;X172_202150104&quot;, &quot;X172_202150105&quot;, &quot;X172_202150106&quot;, &quot;X172_202150107&quot;, &quot;X172_202150200&quot;, &quot;X172_202150300&quot;, &quot;X172_202150301&quot;, &quot;X172_202150302&quot;, &quot;X172_202150303&quot;, &quot;X172_202150304&quot;, &quot;X172_202150305&quot;, &quot;X172_202150400&quot;, &quot;X172_202150500&quot;, &quot;X172_202150501&quot;, &quot;X172_202150502&quot;, &quot;X172_202150503&quot;, &quot;X174_201000000&quot;, &quot;X174_202000000&quot;, &quot;X182_201010000&quot;, &quot;X182_201020000&quot;, &quot;X182_202030000&quot;, &quot;X182_202120000&quot;, &quot;X182_202010000&quot;, &quot;X182_202140000&quot;, &quot;X182_202200100&quot;, &quot;X182_202200200&quot;, &quot;X182_202210100&quot;, &quot;X182_202210200&quot;, &quot;X182_206110100&quot;, &quot;X182_206110200&quot; ]
  },
  &quot;additionalOutput&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;label&quot; : &quot;RemoteAction1&quot;,
  &quot;remoteClass&quot; : &quot;OmnistudioUtils&quot;,
  &quot;remoteMethod&quot; : &quot;checkCustomPermissions&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;responseJSONNode&quot; : &quot;checkCp&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;useFormulas&quot; : true
}</propertySetConfig>
        <sequenceNumber>1.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>CheckIsAbbinato</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;additionalInput&quot; : {
    &quot;accountId&quot; : &quot;%AccountId%&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;label&quot; : &quot;RemoteAction1&quot;,
  &quot;remoteClass&quot; : &quot;OmnistudioUtils&quot;,
  &quot;remoteMethod&quot; : &quot;checkAbbinato&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;responseJSONNode&quot; : &quot;isAbbinato&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;useFormulas&quot; : true
}</propertySetConfig>
        <sequenceNumber>2.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>SetContatoretoToZero</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;elementValueMap&quot; : {
    &quot;conta&quot; : 0
  },
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;label&quot; : &quot;SetValues2&quot;,
  &quot;responseJSONNode&quot; : &quot;Contatore&quot;,
  &quot;responseJSONPath&quot; : &quot;conta&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
                <sequenceNumber>27.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponsesKO</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;%SetConditions:contatoreUnipol% &gt; 0 &amp;&amp; %SetConditions:checkMessageUnipol% == false&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;label&quot; : &quot;ConditionalBlock1&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
            <sequenceNumber>26.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ConditionalBlock8</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;%SetConditions:contatoreUnisalute% &gt; 0 &amp;&amp; %SetConditions:checkMessageUnisalute% == false&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;label&quot; : &quot;ConditionalBlock8&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
        <sequenceNumber>15.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>MergeResponses</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;advancedMerge&quot; : false,
  &quot;advancedMergeMap&quot; : [ ],
  &quot;allowMergeNulls&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;dynamicOutputFields&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;filterListFormula&quot; : &quot;&quot;,
  &quot;hasPrimary&quot; : false,
  &quot;label&quot; : &quot;ListAction1&quot;,
  &quot;mergeFields&quot; : [ ],
  &quot;mergeListsOrder&quot; : [ &quot;respUnipol&quot;, &quot;respUnisalute&quot; ],
  &quot;preventIntraListMerge&quot; : false,
  &quot;primaryListKey&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;finalResponse&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;sortBy&quot; : [ ],
  &quot;sortInDescendingOrder&quot; : false,
  &quot;updateFieldValue&quot; : { },
  &quot;useFormulas&quot; : true
}</propertySetConfig>
                <sequenceNumber>22.0</sequenceNumber>
                <type>List Merge Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>SetContatoreFinal</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;elementValueMap&quot; : {
    &quot;contatore&quot; : &quot;=IF(OR(finalResponse:Message == \&quot;Non ci sono titoli\&quot;,ISNOTBLANK(finalResponse:error:errorMessage)),0,LISTSIZE(finalResponse))&quot;
  },
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;label&quot; : &quot;SetValues1&quot;,
  &quot;responseJSONNode&quot; : &quot;Contatore&quot;,
  &quot;responseJSONPath&quot; : &quot;contatore&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
                <sequenceNumber>23.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>SetTrimFinal</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;elementValueMap&quot; : {
    &quot;newRequest&quot; : &quot;=IF(firstThreeRowsOnly = true, LIST(finalResponse|1, finalResponse|2, finalResponse|3), LIST(finalResponse))&quot;
  },
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;label&quot; : &quot;SetValues1&quot;,
  &quot;responseJSONNode&quot; : &quot;finalResp&quot;,
  &quot;responseJSONPath&quot; : &quot;newRequest&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
                <sequenceNumber>24.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponsesOK</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;%SetConditions:contatoreUnisalute% &gt; 0 &amp;&amp; %SetConditions:checkMessageUnisalute% == true&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;label&quot; : &quot;ConditionalBlock1&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
            <sequenceNumber>21.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ConditionalBlock9</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;%SetConditions:contatoreUnipol% &gt; 0 &amp;&amp; %SetConditions:checkMessageUnipol% == true &quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;label&quot; : &quot;ConditionalBlock9&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
        <sequenceNumber>14.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>GenericLog</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;Message&quot; : &quot;TitoliScadenza&quot;,
    &quot;Payload&quot; : &quot;%TransformResponse%&quot;,
    &quot;ClassName&quot; : &quot;TSTransform&quot;,
    &quot;MethodName&quot; : &quot;TSMethod&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;UniLogIntegrationProcedureSubmit&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;remoteMethod&quot; : &quot;call&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;RemoteAction3&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>17.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>getAccountTitoliInScadenza</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;bundle&quot; : &quot;getAccountTitoliInScadenza&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;element&quot; : &quot;AccountId&quot;,
    &quot;inputParam&quot; : &quot;idToSearch&quot;
  }, {
    &quot;element&quot; : &quot;UserId&quot;,
    &quot;inputParam&quot; : &quot;UserId&quot;
  } ],
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;ignoreCache&quot; : false,
  &quot;label&quot; : &quot;DataMapperExtractAction1&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;show&quot; : null,
  &quot;useFormulas&quot; : true
}</propertySetConfig>
        <sequenceNumber>3.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>getDataUnipol</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(%getDataUnipolSetParams:params%)&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;label&quot; : &quot;HTTPAction1&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;responseJSONNode&quot; : &quot;respUnipol&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;restOptions&quot; : {
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;headers&quot; : {
      &quot;userId&quot; : &quot;%NetworkUserExtract:NetUser%&quot;
    },
    &quot;isCompressed&quot; : false,
    &quot;params&quot; : {
      &quot;agencyCode&quot; : &quot;%getDataUnipolSetParams:params:codiceAgenzia%&quot;,
      &quot;companyCode&quot; : &quot;%getDataUnipolSetParams:params:societa%&quot;,
      &quot;effectiveEndDate&quot; : &quot;%getDataUnipolSetParams:endDate%&quot;,
      &quot;effectiveStartDate&quot; : &quot;%getDataUnipolSetParams:startDate%&quot;,
      &quot;fiscalCode&quot; : &quot;%getDataUnipolSetParams:params:codiceFiscale%&quot;,
      &quot;securityType&quot; : 1,
      &quot;tipoMovimento&quot; : &quot;ARRETRATO&quot;
    },
    &quot;sendBody&quot; : false,
    &quot;timeout&quot; : null,
    &quot;xmlEscapeResponse&quot; : false
  },
  &quot;restPath&quot; : &quot;/api/v1/titoli&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;useFormulas&quot; : true
}</propertySetConfig>
        <sequenceNumber>7.0</sequenceNumber>
        <type>Rest Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>getDataUnipolSetParams</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;elementValueMap&quot; : {
    &quot;endDate&quot; : &quot;=FORMATDATETIME(ADDDAY((TODAY), 60),\&quot;yyyy-MM-dd\&quot;)&quot;,
    &quot;params&quot; : &quot;=FILTER(LIST(%getAccountTitoliInScadenza:Account%), &apos;societa=\&quot;1\&quot;&apos;)&quot;,
    &quot;startDate&quot; : &quot;1900-01-01&quot;
  },
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;label&quot; : &quot;SetValues3&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
        <sequenceNumber>5.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>getDataUniSalute</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(%getDataUniSaluteSetParams:params%)&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;label&quot; : &quot;HTTPAction1&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;responseJSONNode&quot; : &quot;respUnisalute&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;restOptions&quot; : {
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;headers&quot; : {
      &quot;userId&quot; : &quot;%NetworkUserExtract:NetUser%&quot;
    },
    &quot;isCompressed&quot; : false,
    &quot;params&quot; : {
      &quot;agencyCode&quot; : &quot;%getDataUniSaluteSetParams:params:codiceAgenzia%&quot;,
      &quot;companyCode&quot; : &quot;%getDataUniSaluteSetParams:params:societa%&quot;,
      &quot;effectiveEndDate&quot; : &quot;%getDataUniSaluteSetParams:endDate%&quot;,
      &quot;effectiveStartDate&quot; : &quot;%getDataUniSaluteSetParams:startDate%&quot;,
      &quot;fiscalCode&quot; : &quot;%getDataUniSaluteSetParams:params:codiceFiscale%&quot;,
      &quot;securityType&quot; : 1,
      &quot;tipoMovimento&quot; : &quot;ARRETRATO&quot;
    },
    &quot;sendBody&quot; : false,
    &quot;timeout&quot; : null,
    &quot;xmlEscapeResponse&quot; : false
  },
  &quot;restPath&quot; : &quot;/api/v1/titoli&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;useFormulas&quot; : true
}</propertySetConfig>
        <sequenceNumber>9.0</sequenceNumber>
        <type>Rest Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>getDataUniSaluteSetParams</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;elementValueMap&quot; : {
    &quot;endDate&quot; : &quot;=FORMATDATETIME(ADDDAY((TODAY), 60),\&quot;yyyy-MM-dd\&quot;)&quot;,
    &quot;params&quot; : &quot;=FILTER(LIST(%getAccountTitoliInScadenza:Account%), &apos;societa=\&quot;4\&quot;&apos;)&quot;,
    &quot;startDate&quot; : &quot;1900-01-01&quot;
  },
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;label&quot; : &quot;SetValues4&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
        <sequenceNumber>6.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>MockUnipol</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;elementValueMap&quot; : {
    &quot;responseUnipol&quot; : &quot;=DESERIALIZE(&apos;[{\&quot;uplCrmQuietId\&quot;:\&quot;\&quot;,\&quot;idContrattoPTF\&quot;:\&quot;f5d270eb-0feb-4083-b366-60b7aaa726cd\&quot;,\&quot;xquietanzaId\&quot;:\&quot;213631529\&quot;,\&quot;contId\&quot;:\&quot;922339426762273302\&quot;,\&quot;isFEAAttiva\&quot;:false,\&quot;isFEAVisualizzata\&quot;:true,\&quot;isProdottoUnico\&quot;:true,\&quot;isFirmaAttiva\&quot;:true,\&quot;isRettificaAttiva\&quot;:true,\&quot;isIncassaAttiva\&quot;:true,\&quot;isCompletaAttiva\&quot;:false,\&quot;Unibox\&quot;:\&quot;\&quot;,\&quot;Firmatario\&quot;:\&quot;\&quot;,\&quot;Rettificabile\&quot;:\&quot;\&quot;,\&quot;Titoli al legale\&quot;:\&quot;\&quot;,\&quot;Modello\&quot;:\&quot;\&quot;,\&quot;Targa\&quot;:\&quot;\&quot;,\&quot;Esposizione AR\&quot;:false,\&quot;Omnicanalità\&quot;:\&quot;\&quot;,\&quot;Frazionamento\&quot;:\&quot;MENSILE\&quot;,\&quot;N. Folder\&quot;:\&quot;101853000292438\&quot;,\&quot;Premio\&quot;:0,\&quot;Scadenza\&quot;:\&quot;2025-06-23T22:00:00\&quot;,\&quot;Numero Polizza/Posizione\&quot;:[194024365],\&quot;Ambito\&quot;:\&quot;\&quot;,\&quot;Tipo\&quot;:\&quot;4\&quot;,\&quot;Società\&quot;:\&quot;1\&quot;},{\&quot;uplCrmQuietId\&quot;:\&quot;\&quot;,\&quot;idContrattoPTF\&quot;:\&quot;c783733c-0eba-4cdc-93ff-ef5338436e1d\&quot;,\&quot;xquietanzaId\&quot;:\&quot;213631525\&quot;,\&quot;contId\&quot;:\&quot;922339426762273302\&quot;,\&quot;isFEAAttiva\&quot;:false,\&quot;isFEAVisualizzata\&quot;:true,\&quot;isProdottoUnico\&quot;:true,\&quot;isFirmaAttiva\&quot;:true,\&quot;isRettificaAttiva\&quot;:true,\&quot;isIncassaAttiva\&quot;:true,\&quot;isCompletaAttiva\&quot;:false,\&quot;Unibox\&quot;:\&quot;\&quot;,\&quot;Firmatario\&quot;:\&quot;\&quot;,\&quot;Rettificabile\&quot;:\&quot;\&quot;,\&quot;Titoli al legale\&quot;:\&quot;\&quot;,\&quot;Modello\&quot;:\&quot;\&quot;,\&quot;Targa\&quot;:\&quot;\&quot;,\&quot;Esposizione AR\&quot;:false,\&quot;Omnicanalità\&quot;:\&quot;\&quot;,\&quot;Frazionamento\&quot;:\&quot;MENSILE\&quot;,\&quot;N. Folder\&quot;:\&quot;101853000292426\&quot;,\&quot;Premio\&quot;:0,\&quot;Scadenza\&quot;:\&quot;2025-06-23T22:00:00\&quot;,\&quot;Numero Polizza/Posizione\&quot;:[192165386],\&quot;Ambito\&quot;:\&quot;\&quot;,\&quot;Tipo\&quot;:\&quot;4\&quot;,\&quot;Società\&quot;:\&quot;1\&quot;},{\&quot;uplCrmQuietId\&quot;:\&quot;\&quot;,\&quot;idContrattoPTF\&quot;:\&quot;fc6fc21a-9ba6-413c-894c-fb00b04ea34a\&quot;,\&quot;xquietanzaId\&quot;:\&quot;213622222\&quot;,\&quot;contId\&quot;:\&quot;922339426762273302\&quot;,\&quot;isFEAAttiva\&quot;:false,\&quot;isFEAVisualizzata\&quot;:true,\&quot;isProdottoUnico\&quot;:true,\&quot;isFirmaAttiva\&quot;:true,\&quot;isRettificaAttiva\&quot;:true,\&quot;isIncassaAttiva\&quot;:true,\&quot;isCompletaAttiva\&quot;:false,\&quot;Unibox\&quot;:\&quot;\&quot;,\&quot;Firmatario\&quot;:\&quot;\&quot;,\&quot;Rettificabile\&quot;:\&quot;\&quot;,\&quot;Titoli al legale\&quot;:\&quot;\&quot;,\&quot;Modello\&quot;:\&quot;\&quot;,\&quot;Targa\&quot;:\&quot;\&quot;,\&quot;Esposizione AR\&quot;:false,\&quot;Omnicanalità\&quot;:\&quot;\&quot;,\&quot;Frazionamento\&quot;:\&quot;MENSILE\&quot;,\&quot;N. Folder\&quot;:\&quot;101853000247030\&quot;,\&quot;Premio\&quot;:0,\&quot;Scadenza\&quot;:\&quot;2025-06-27T22:00:00\&quot;,\&quot;Numero Polizza/Posizione\&quot;:[192139688],\&quot;Ambito\&quot;:\&quot;\&quot;,\&quot;Tipo\&quot;:\&quot;4\&quot;,\&quot;Società\&quot;:\&quot;1\&quot;},{\&quot;uplCrmQuietId\&quot;:\&quot;\&quot;,\&quot;idContrattoPTF\&quot;:\&quot;7ae53956-c050-4cbd-bab6-ff71460b0aaa\&quot;,\&quot;xquietanzaId\&quot;:\&quot;213622148\&quot;,\&quot;contId\&quot;:\&quot;922339426762273302\&quot;,\&quot;isFEAAttiva\&quot;:false,\&quot;isFEAVisualizzata\&quot;:true,\&quot;isProdottoUnico\&quot;:true,\&quot;isFirmaAttiva\&quot;:true,\&quot;isRettificaAttiva\&quot;:true,\&quot;isIncassaAttiva\&quot;:true,\&quot;isCompletaAttiva\&quot;:false,\&quot;Unibox\&quot;:\&quot;\&quot;,\&quot;Firmatario\&quot;:\&quot;\&quot;,\&quot;Rettificabile\&quot;:\&quot;\&quot;,\&quot;Titoli al legale\&quot;:\&quot;\&quot;,\&quot;Modello\&quot;:\&quot;\&quot;,\&quot;Targa\&quot;:\&quot;\&quot;,\&quot;Esposizione AR\&quot;:false,\&quot;Omnicanalità\&quot;:\&quot;\&quot;,\&quot;Frazionamento\&quot;:\&quot;MENSILE\&quot;,\&quot;N. Folder\&quot;:\&quot;101853000246509\&quot;,\&quot;Premio\&quot;:0,\&quot;Scadenza\&quot;:\&quot;2025-06-27T22:00:00\&quot;,\&quot;Numero Polizza/Posizione\&quot;:[192139230],\&quot;Ambito\&quot;:\&quot;\&quot;,\&quot;Tipo\&quot;:\&quot;4\&quot;,\&quot;Società\&quot;:\&quot;1\&quot;},{\&quot;uplCrmQuietId\&quot;:\&quot;\&quot;,\&quot;idContrattoPTF\&quot;:\&quot;e9576864-0dee-4bbc-8565-f1cc2c00db79\&quot;,\&quot;xquietanzaId\&quot;:\&quot;213622267\&quot;,\&quot;contId\&quot;:\&quot;922339426762273302\&quot;,\&quot;isFEAAttiva\&quot;:false,\&quot;isFEAVisualizzata\&quot;:true,\&quot;isProdottoUnico\&quot;:true,\&quot;isFirmaAttiva\&quot;:true,\&quot;isRettificaAttiva\&quot;:true,\&quot;isIncassaAttiva\&quot;:true,\&quot;isCompletaAttiva\&quot;:false,\&quot;Unibox\&quot;:\&quot;\&quot;,\&quot;Firmatario\&quot;:\&quot;\&quot;,\&quot;Rettificabile\&quot;:\&quot;\&quot;,\&quot;Titoli al legale\&quot;:\&quot;\&quot;,\&quot;Modello\&quot;:\&quot;\&quot;,\&quot;Targa\&quot;:\&quot;\&quot;,\&quot;Esposizione AR\&quot;:false,\&quot;Omnicanalità\&quot;:\&quot;\&quot;,\&quot;Frazionamento\&quot;:\&quot;MENSILE\&quot;,\&quot;N. Folder\&quot;:\&quot;101853000247687\&quot;,\&quot;Premio\&quot;:0,\&quot;Scadenza\&quot;:\&quot;2025-06-29T22:00:00\&quot;,\&quot;Numero Polizza/Posizione\&quot;:[192140242],\&quot;Ambito\&quot;:\&quot;\&quot;,\&quot;Tipo\&quot;:\&quot;4\&quot;,\&quot;Società\&quot;:\&quot;1\&quot;},{\&quot;uplCrmQuietId\&quot;:\&quot;\&quot;,\&quot;idContrattoPTF\&quot;:\&quot;7359394b-c6e5-4779-872a-e819f9533350\&quot;,\&quot;xquietanzaId\&quot;:\&quot;213621742\&quot;,\&quot;contId\&quot;:\&quot;922339426762273302\&quot;,\&quot;isFEAAttiva\&quot;:false,\&quot;isFEAVisualizzata\&quot;:true,\&quot;isProdottoUnico\&quot;:true,\&quot;isFirmaAttiva\&quot;:true,\&quot;isRettificaAttiva\&quot;:true,\&quot;isIncassaAttiva\&quot;:true,\&quot;isCompletaAttiva\&quot;:false,\&quot;Unibox\&quot;:\&quot;\&quot;,\&quot;Firmatario\&quot;:\&quot;\&quot;,\&quot;Rettificabile\&quot;:\&quot;\&quot;,\&quot;Titoli al legale\&quot;:\&quot;\&quot;,\&quot;Modello\&quot;:\&quot;\&quot;,\&quot;Targa\&quot;:\&quot;\&quot;,\&quot;Esposizione AR\&quot;:false,\&quot;Omnicanalità\&quot;:\&quot;\&quot;,\&quot;Frazionamento\&quot;:\&quot;MENSILE\&quot;,\&quot;N. Folder\&quot;:\&quot;101853000212733\&quot;,\&quot;Premio\&quot;:0,\&quot;Scadenza\&quot;:\&quot;2025-07-20T22:00:00\&quot;,\&quot;Numero Polizza/Posizione\&quot;:[192135424],\&quot;Ambito\&quot;:\&quot;\&quot;,\&quot;Tipo\&quot;:\&quot;4\&quot;,\&quot;Società\&quot;:\&quot;1\&quot;},{\&quot;uplCrmQuietId\&quot;:\&quot;\&quot;,\&quot;idContrattoPTF\&quot;:\&quot;f5d270eb-0feb-4083-b366-60b7aaa726cd\&quot;,\&quot;xquietanzaId\&quot;:\&quot;213631528\&quot;,\&quot;contId\&quot;:\&quot;922339426762273302\&quot;,\&quot;isFEAAttiva\&quot;:false,\&quot;isFEAVisualizzata\&quot;:true,\&quot;isProdottoUnico\&quot;:true,\&quot;isFirmaAttiva\&quot;:true,\&quot;isRettificaAttiva\&quot;:true,\&quot;isIncassaAttiva\&quot;:true,\&quot;isCompletaAttiva\&quot;:false,\&quot;Unibox\&quot;:\&quot;\&quot;,\&quot;Firmatario\&quot;:\&quot;\&quot;,\&quot;Rettificabile\&quot;:\&quot;\&quot;,\&quot;Titoli al legale\&quot;:\&quot;\&quot;,\&quot;Modello\&quot;:\&quot;\&quot;,\&quot;Targa\&quot;:\&quot;\&quot;,\&quot;Esposizione AR\&quot;:false,\&quot;Omnicanalità\&quot;:\&quot;\&quot;,\&quot;Frazionamento\&quot;:\&quot;MENSILE\&quot;,\&quot;N. Folder\&quot;:\&quot;101853000292438\&quot;,\&quot;Premio\&quot;:0,\&quot;Scadenza\&quot;:\&quot;2025-07-23T22:00:00\&quot;,\&quot;Numero Polizza/Posizione\&quot;:[194024365],\&quot;Ambito\&quot;:\&quot;\&quot;,\&quot;Tipo\&quot;:\&quot;4\&quot;,\&quot;Società\&quot;:\&quot;1\&quot;},{\&quot;uplCrmQuietId\&quot;:\&quot;\&quot;,\&quot;idContrattoPTF\&quot;:\&quot;c783733c-0eba-4cdc-93ff-ef5338436e1d\&quot;,\&quot;xquietanzaId\&quot;:\&quot;213631520\&quot;,\&quot;contId\&quot;:\&quot;922339426762273302\&quot;,\&quot;isFEAAttiva\&quot;:false,\&quot;isFEAVisualizzata\&quot;:true,\&quot;isProdottoUnico\&quot;:true,\&quot;isFirmaAttiva\&quot;:true,\&quot;isRettificaAttiva\&quot;:true,\&quot;isIncassaAttiva\&quot;:true,\&quot;isCompletaAttiva\&quot;:false,\&quot;Unibox\&quot;:\&quot;\&quot;,\&quot;Firmatario\&quot;:\&quot;\&quot;,\&quot;Rettificabile\&quot;:\&quot;\&quot;,\&quot;Titoli al legale\&quot;:\&quot;\&quot;,\&quot;Modello\&quot;:\&quot;\&quot;,\&quot;Targa\&quot;:\&quot;\&quot;,\&quot;Esposizione AR\&quot;:false,\&quot;Omnicanalità\&quot;:\&quot;\&quot;,\&quot;Frazionamento\&quot;:\&quot;MENSILE\&quot;,\&quot;N. Folder\&quot;:\&quot;101853000292426\&quot;,\&quot;Premio\&quot;:0,\&quot;Scadenza\&quot;:\&quot;2025-07-23T22:00:00\&quot;,\&quot;Numero Polizza/Posizione\&quot;:[192165386],\&quot;Ambito\&quot;:\&quot;\&quot;,\&quot;Tipo\&quot;:\&quot;4\&quot;,\&quot;Società\&quot;:\&quot;1\&quot;},{\&quot;uplCrmQuietId\&quot;:\&quot;\&quot;,\&quot;idContrattoPTF\&quot;:\&quot;fc6fc21a-9ba6-413c-894c-fb00b04ea34a\&quot;,\&quot;xquietanzaId\&quot;:\&quot;213622220\&quot;,\&quot;contId\&quot;:\&quot;922339426762273302\&quot;,\&quot;isFEAAttiva\&quot;:false,\&quot;isFEAVisualizzata\&quot;:true,\&quot;isProdottoUnico\&quot;:true,\&quot;isFirmaAttiva\&quot;:true,\&quot;isRettificaAttiva\&quot;:true,\&quot;isIncassaAttiva\&quot;:true,\&quot;isCompletaAttiva\&quot;:false,\&quot;Unibox\&quot;:\&quot;\&quot;,\&quot;Firmatario\&quot;:\&quot;\&quot;,\&quot;Rettificabile\&quot;:\&quot;\&quot;,\&quot;Titoli al legale\&quot;:\&quot;\&quot;,\&quot;Modello\&quot;:\&quot;\&quot;,\&quot;Targa\&quot;:\&quot;\&quot;,\&quot;Esposizione AR\&quot;:false,\&quot;Omnicanalità\&quot;:\&quot;\&quot;,\&quot;Frazionamento\&quot;:\&quot;MENSILE\&quot;,\&quot;N. Folder\&quot;:\&quot;101853000247030\&quot;,\&quot;Premio\&quot;:0,\&quot;Scadenza\&quot;:\&quot;2025-07-27T22:00:00\&quot;,\&quot;Numero Polizza/Posizione\&quot;:[192139688],\&quot;Ambito\&quot;:\&quot;\&quot;,\&quot;Tipo\&quot;:\&quot;4\&quot;,\&quot;Società\&quot;:\&quot;1\&quot;},{\&quot;uplCrmQuietId\&quot;:\&quot;\&quot;,\&quot;idContrattoPTF\&quot;:\&quot;7ae53956-c050-4cbd-bab6-ff71460b0aaa\&quot;,\&quot;xquietanzaId\&quot;:\&quot;213622141\&quot;,\&quot;contId\&quot;:\&quot;922339426762273302\&quot;,\&quot;isFEAAttiva\&quot;:false,\&quot;isFEAVisualizzata\&quot;:true,\&quot;isProdottoUnico\&quot;:true,\&quot;isFirmaAttiva\&quot;:true,\&quot;isRettificaAttiva\&quot;:true,\&quot;isIncassaAttiva\&quot;:true,\&quot;isCompletaAttiva\&quot;:false,\&quot;Unibox\&quot;:\&quot;\&quot;,\&quot;Firmatario\&quot;:\&quot;\&quot;,\&quot;Rettificabile\&quot;:\&quot;\&quot;,\&quot;Titoli al legale\&quot;:\&quot;\&quot;,\&quot;Modello\&quot;:\&quot;\&quot;,\&quot;Targa\&quot;:\&quot;\&quot;,\&quot;Esposizione AR\&quot;:false,\&quot;Omnicanalità\&quot;:\&quot;\&quot;,\&quot;Frazionamento\&quot;:\&quot;MENSILE\&quot;,\&quot;N. Folder\&quot;:\&quot;101853000246509\&quot;,\&quot;Premio\&quot;:0,\&quot;Scadenza\&quot;:\&quot;2025-07-27T22:00:00\&quot;,\&quot;Numero Polizza/Posizione\&quot;:[192139230],\&quot;Ambito\&quot;:\&quot;\&quot;,\&quot;Tipo\&quot;:\&quot;4\&quot;,\&quot;Società\&quot;:\&quot;1\&quot;}]&apos;)&quot;
  },
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;label&quot; : &quot;SetValues3&quot;,
  &quot;responseJSONNode&quot; : &quot;respUnipol&quot;,
  &quot;responseJSONPath&quot; : &quot;responseUnipol&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
        <sequenceNumber>8.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>MockUnisalute</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;elementValueMap&quot; : {
    &quot;responseUnisalute&quot; : &quot;=DESERIALIZE(&apos;[\n  {\n    \&quot;uplCrmQuietId\&quot;: \&quot;\&quot;,\n    \&quot;idContrattoPTF\&quot;: \&quot;f5d270eb-0feb-4083-b366-60b7aaa726cd\&quot;,\n    \&quot;xquietanzaId\&quot;: \&quot;213631529\&quot;,\n    \&quot;contId\&quot;: \&quot;922339426762273302\&quot;,\n    \&quot;isFEAAttiva\&quot;: false,\n    \&quot;isFEAVisualizzata\&quot;: true,\n    \&quot;isProdottoUnico\&quot;: true,\n    \&quot;isFirmaAttiva\&quot;: true,\n    \&quot;isRettificaAttiva\&quot;: true,\n    \&quot;isIncassaAttiva\&quot;: true,\n    \&quot;isCompletaAttiva\&quot;: false,\n    \&quot;Unibox\&quot;: \&quot;\&quot;,\n    \&quot;Firmatario\&quot;: \&quot;\&quot;,\n    \&quot;Rettificabile\&quot;: \&quot;\&quot;,\n    \&quot;Titoli al legale\&quot;: \&quot;\&quot;,\n    \&quot;Modello\&quot;: \&quot;\&quot;,\n    \&quot;Targa\&quot;: \&quot;\&quot;,\n    \&quot;Esposizione AR\&quot;: false,\n    \&quot;Omnicanalità\&quot;: \&quot;\&quot;,\n    \&quot;Frazionamento\&quot;: \&quot;MENSILE\&quot;,\n    \&quot;N. Folder\&quot;: \&quot;101853000292438\&quot;,\n    \&quot;Premio\&quot;: 0,\n    \&quot;Scadenza\&quot;: \&quot;2025-06-23T22:00:00\&quot;,\n    \&quot;Numero Polizza/Posizione\&quot;: [\n      19402498365\n    ],\n    \&quot;Ambito\&quot;: \&quot;\&quot;,\n    \&quot;Tipo\&quot;: \&quot;4\&quot;,\n    \&quot;Società\&quot;: \&quot;4\&quot;\n  },\n  {\n    \&quot;uplCrmQuietId\&quot;: \&quot;\&quot;,\n    \&quot;idContrattoPTF\&quot;: \&quot;c783733c-0eba-4cdc-93ff-ef5338436e1d\&quot;,\n    \&quot;xquietanzaId\&quot;: \&quot;213631525\&quot;,\n    \&quot;contId\&quot;: \&quot;922339426762273302\&quot;,\n    \&quot;isFEAAttiva\&quot;: false,\n    \&quot;isFEAVisualizzata\&quot;: true,\n    \&quot;isProdottoUnico\&quot;: true,\n    \&quot;isFirmaAttiva\&quot;: true,\n    \&quot;isRettificaAttiva\&quot;: true,\n    \&quot;isIncassaAttiva\&quot;: true,\n    \&quot;isCompletaAttiva\&quot;: false,\n    \&quot;Unibox\&quot;: \&quot;\&quot;,\n    \&quot;Firmatario\&quot;: \&quot;\&quot;,\n    \&quot;Rettificabile\&quot;: \&quot;\&quot;,\n    \&quot;Titoli al legale\&quot;: \&quot;\&quot;,\n    \&quot;Modello\&quot;: \&quot;\&quot;,\n    \&quot;Targa\&quot;: \&quot;\&quot;,\n    \&quot;Esposizione AR\&quot;: false,\n    \&quot;Omnicanalità\&quot;: \&quot;\&quot;,\n    \&quot;Frazionamento\&quot;: \&quot;MENSILE\&quot;,\n    \&quot;N. Folder\&quot;: \&quot;101853000292426\&quot;,\n    \&quot;Premio\&quot;: 0,\n    \&quot;Scadenza\&quot;: \&quot;2025-06-23T22:00:00\&quot;,\n    \&quot;Numero Polizza/Posizione\&quot;: [\n      1999885386\n    ],\n    \&quot;Ambito\&quot;: \&quot;\&quot;,\n    \&quot;Tipo\&quot;: \&quot;4\&quot;,\n    \&quot;Società\&quot;: \&quot;4\&quot;\n  },\n  {\n    \&quot;uplCrmQuietId\&quot;: \&quot;\&quot;,\n    \&quot;idContrattoPTF\&quot;: \&quot;fc6fc21a-9ba6-413c-894c-fb00b04ea34a\&quot;,\n    \&quot;xquietanzaId\&quot;: \&quot;213622222\&quot;,\n    \&quot;contId\&quot;: \&quot;922339426762273302\&quot;,\n    \&quot;isFEAAttiva\&quot;: false,\n    \&quot;isFEAVisualizzata\&quot;: true,\n    \&quot;isProdottoUnico\&quot;: true,\n    \&quot;isFirmaAttiva\&quot;: true,\n    \&quot;isRettificaAttiva\&quot;: true,\n    \&quot;isIncassaAttiva\&quot;: true,\n    \&quot;isCompletaAttiva\&quot;: false,\n    \&quot;Unibox\&quot;: \&quot;\&quot;,\n    \&quot;Firmatario\&quot;: \&quot;\&quot;,\n    \&quot;Rettificabile\&quot;: \&quot;\&quot;,\n    \&quot;Titoli al legale\&quot;: \&quot;\&quot;,\n    \&quot;Modello\&quot;: \&quot;\&quot;,\n    \&quot;Targa\&quot;: \&quot;\&quot;,\n    \&quot;Esposizione AR\&quot;: false,\n    \&quot;Omnicanalità\&quot;: \&quot;\&quot;,\n    \&quot;Frazionamento\&quot;: \&quot;MENSILE\&quot;,\n    \&quot;N. Folder\&quot;: \&quot;101853000247030\&quot;,\n    \&quot;Premio\&quot;: 0,\n    \&quot;Scadenza\&quot;: \&quot;2025-06-27T22:00:00\&quot;,\n    \&quot;Numero Polizza/Posizione\&quot;: [\n      192632688\n    ],\n    \&quot;Ambito\&quot;: \&quot;\&quot;,\n    \&quot;Tipo\&quot;: \&quot;4\&quot;,\n    \&quot;Società\&quot;: \&quot;4\&quot;\n  }\n]&apos;)&quot;
  },
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;label&quot; : &quot;SetValues3&quot;,
  &quot;responseJSONNode&quot; : &quot;respUnisalute&quot;,
  &quot;responseJSONPath&quot; : &quot;responseUnisalute&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
        <sequenceNumber>10.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>NetworkUserExtract</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;bundle&quot; : &quot;NetworkUserExtract&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;element&quot; : &quot;UserId&quot;,
    &quot;inputParam&quot; : &quot;UserId&quot;
  } ],
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;ignoreCache&quot; : false,
  &quot;label&quot; : &quot;DataMapperExtractAction2&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;useFormulas&quot; : true
}</propertySetConfig>
        <sequenceNumber>4.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetContatoreUnipol</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;elementValueMap&quot; : {
    &quot;conta&quot; : &quot;=IF(OR(respUnipol:Message == \&quot;Non ci sono titoli\&quot;,ISNOTBLANK(respUnipol:error:errorMessage)),0, LISTSIZE(respUnipol))&quot;
  },
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;label&quot; : &quot;SetValues2&quot;,
  &quot;responseJSONNode&quot; : &quot;Contatore&quot;,
  &quot;responseJSONPath&quot; : &quot;conta&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
            <sequenceNumber>14.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetFinalResponseUnipol</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;elementValueMap&quot; : {
    &quot;UnipolResponse&quot; : &quot;%respUnipol%&quot;
  },
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;label&quot; : &quot;SetValues7&quot;,
  &quot;responseJSONNode&quot; : &quot;finalResponse&quot;,
  &quot;responseJSONPath&quot; : &quot;UnipolResponse&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
            <sequenceNumber>13.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetTrimUnipolOK</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;elementValueMap&quot; : {
    &quot;newRequest&quot; : &quot;=IF(firstThreeRowsOnly = true, LIST(finalResponse|1, finalResponse|2, finalResponse|3), LIST(finalResponse))&quot;
  },
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;label&quot; : &quot;SetValues1&quot;,
  &quot;responseJSONNode&quot; : &quot;finalResp&quot;,
  &quot;responseJSONPath&quot; : &quot;newRequest&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
            <sequenceNumber>15.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <description>%SetCondtions:checkMessageUnipol% == true %SetCondtions:checkMessageUnipol% == true</description>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ResponseUnipolOK_ResponseUnisaluteKO</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;%SetConditions:contatoreUnipol% &gt; 0 &amp;&amp; %SetConditions:checkMessageUnipol% == true&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;label&quot; : &quot;ConditionalBlock1&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
        <sequenceNumber>12.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetContatoreUnisalute</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;elementValueMap&quot; : {
    &quot;conta&quot; : &quot;=IF(OR(respUnisalute:Message == \&quot;Non ci sono titoli\&quot;,ISNOTBLANK(respUnisalute:error:errorMessage)),0, LISTSIZE(respUnisalute))&quot;
  },
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;label&quot; : &quot;SetValues2&quot;,
  &quot;responseJSONNode&quot; : &quot;Contatore&quot;,
  &quot;responseJSONPath&quot; : &quot;conta&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
            <sequenceNumber>18.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetFinalResponseUnisalute</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;elementValueMap&quot; : {
    &quot;UnisaluteResponse&quot; : &quot;%respUnisalute%&quot;
  },
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;label&quot; : &quot;SetValues7&quot;,
  &quot;responseJSONNode&quot; : &quot;finalResponse&quot;,
  &quot;responseJSONPath&quot; : &quot;UnisaluteResponse&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
            <sequenceNumber>17.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetTrimUnisaluteOK</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;elementValueMap&quot; : {
    &quot;newRequest&quot; : &quot;=IF(firstThreeRowsOnly = true, LIST(finalResponse|1, finalResponse|2, finalResponse|3), LIST(finalResponse))&quot;
  },
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;label&quot; : &quot;SetValues1&quot;,
  &quot;responseJSONNode&quot; : &quot;finalResp&quot;,
  &quot;responseJSONPath&quot; : &quot;newRequest&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
            <sequenceNumber>19.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ResponseUnisaluteOK_ResponseUnipolKO</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;%SetConditions:contatoreUnisalute% &gt; 0 &amp;&amp; %SetConditions:checkMessageUnisalute% == true&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;label&quot; : &quot;ConditionalBlock1&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
        <sequenceNumber>13.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ReturnResponse</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;additionalOutput&quot; : {
    &quot;Contatore&quot; : &quot;=TOSTRING(Contatore)&quot;,
    &quot;Response&quot; : &quot;%TransformResponse%&quot;
  },
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;label&quot; : &quot;ResponseAction1&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;returnFullDataJSON&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;useFormulas&quot; : true,
  &quot;vlcResponseHeaders&quot; : { }
}</propertySetConfig>
        <sequenceNumber>18.0</sequenceNumber>
        <type>Response Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetConditions</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;elementValueMap&quot; : {
    &quot;checkMessageUnipol&quot; : &quot;=ISBLANK(%respUnipol:Message%)&quot;,
    &quot;checkMessageUnisalute&quot; : &quot;=ISBLANK(%respUnisalute:Message%)&quot;,
    &quot;contatoreUnipol&quot; : &quot;=LISTSIZE(respUnipol)&quot;,
    &quot;contatoreUnisalute&quot; : &quot;=LISTSIZE(respUnisalute)&quot;
  },
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;label&quot; : &quot;SetValues14&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
        <sequenceNumber>11.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>TransformResponse</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : {
    &quot;IsUnipol&quot; : &quot;%getAccountTitoliInScadenza:permissionSetAssignmentUnipolSai%&quot;,
    &quot;IsUnisalute&quot; : &quot;%getAccountTitoliInScadenza:permissionSetAssignmentUnisalute%&quot;,
    &quot;Society&quot; : &quot;%getAccountTitoliInScadenza:accountSociety%&quot;,
    &quot;isAbbinato&quot; : &quot;%isAbbinato%&quot;
  },
  &quot;bundle&quot; : &quot;TitoliInScadenzaTransform&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;ignoreCache&quot; : false,
  &quot;label&quot; : &quot;DataMapperTransformAction1&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;finalResp&quot;,
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;useFormulas&quot; : true
}</propertySetConfig>
        <sequenceNumber>16.0</sequenceNumber>
        <type>DataRaptor Transform Action</type>
    </omniProcessElements>
    <omniProcessKey>UniDS_TitoliInScadenza</omniProcessKey>
    <omniProcessType>Integration Procedure</omniProcessType>
    <propertySetConfig>{
  &quot;linkToExternalObject&quot; : &quot;&quot;,
  &quot;trackingCustomData&quot; : { },
  &quot;includeAllActionsInResponse&quot; : false,
  &quot;columnsPropertyMap&quot; : [ ],
  &quot;relationshipFieldsMap&quot; : [ ],
  &quot;labelSingular&quot; : &quot;&quot;,
  &quot;labelPlural&quot; : &quot;&quot;,
  &quot;description&quot; : &quot;&quot;,
  &quot;nameColumn&quot; : &quot;&quot;,
  &quot;rollbackOnError&quot; : false,
  &quot;chainableQueriesLimit&quot; : 50,
  &quot;chainableDMLStatementsLimit&quot; : null,
  &quot;chainableCpuLimit&quot; : 2000,
  &quot;chainableHeapSizeLimit&quot; : null,
  &quot;chainableDMLRowsLimit&quot; : null,
  &quot;chainableQueryRowsLimit&quot; : null,
  &quot;chainableSoslQueriesLimit&quot; : null,
  &quot;chainableActualTimeLimit&quot; : null,
  &quot;additionalChainableResponse&quot; : { },
  &quot;queueableChainableQueriesLimit&quot; : 120,
  &quot;queueableChainableCpuLimit&quot; : 40000,
  &quot;queueableChainableHeapSizeLimit&quot; : 6,
  &quot;ttlMinutes&quot; : 5,
  &quot;mockResponseMap&quot; : { }
}</propertySetConfig>
    <subType>TitoliInScadenza</subType>
    <type>UniDS</type>
    <uniqueName>UniDS_TitoliInScadenza_Procedure_11</uniqueName>
    <versionNumber>11.0</versionNumber>
    <webComponentKey>988b60bf-0f4b-6820-02df-4babdef04363</webComponentKey>
</OmniIntegrationProcedure>
