<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <clonedFromOmniUiCardKey>SS_Task_In_Scadenza_Test/Unipolsai/1.0</clonedFromOmniUiCardKey>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;contextVariables&quot;:[{&quot;id&quot;:1,&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;0019O00000rv4A2QAI&quot;}],&quot;orderBy&quot;:{},&quot;type&quot;:null,&quot;value&quot;:{}},&quot;state0element1_0&quot;:{&quot;contextVariables&quot;:[],&quot;orderBy&quot;:{},&quot;type&quot;:null,&quot;value&quot;:{&quot;bundleType&quot;:&quot;Extract&quot;}}}</dataSourceConfig>
    <description>New Titoli in scadenza child Flexcard to manage the DataTable</description>
    <isActive>true</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>UniTitoliInScadenza_Child_DataTable</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;dataSource&quot;:{&quot;contextVariables&quot;:[{&quot;id&quot;:1,&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;0019O00000rv4A2QAI&quot;}],&quot;orderBy&quot;:{},&quot;type&quot;:null,&quot;value&quot;:{}},&quot;dynamicCanvasWidth&quot;:{&quot;type&quot;:&quot;desktop&quot;},&quot;enableLwc&quot;:true,&quot;globalCSS&quot;:false,&quot;isFlex&quot;:true,&quot;isRepeatable&quot;:false,&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfUniTitoliInScadenza_Child_DataTable&quot;,&quot;Id&quot;:&quot;0Rb9X000007Ymm9SAC&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;,&quot;MasterLabel&quot;:&quot;cfUniTitoliInScadenza_Child_DataTable&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;},&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;selectedCardsLabel&quot;:&quot;DettaglioTitoliScadenza&quot;,&quot;states&quot;:[{&quot;actions&quot;:[],&quot;blankCardState&quot;:false,&quot;childCards&quot;:[],&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;class&quot;:&quot;slds-col &quot;,&quot;element&quot;:&quot;flexDatatable&quot;,&quot;elementLabel&quot;:&quot;Datatable-1&quot;,&quot;name&quot;:&quot;Datatable&quot;,&quot;property&quot;:{&quot;card&quot;:&quot;{card}&quot;,&quot;cellLevelEdit&quot;:true,&quot;columns&quot;:[{&quot;fieldName&quot;:&quot;compagnia&quot;,&quot;label&quot;:&quot;Società&quot;,&quot;searchable&quot;:false,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;tipo&quot;,&quot;label&quot;:&quot;Tipo&quot;,&quot;searchable&quot;:false,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;ambito&quot;,&quot;label&quot;:&quot;Ambito&quot;,&quot;searchable&quot;:false,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;polizza&quot;,&quot;label&quot;:&quot;Numero Polizza/Posizione&quot;,&quot;searchable&quot;:false,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;dataScadenzaTitolo&quot;,&quot;label&quot;:&quot;Scadenza&quot;,&quot;searchable&quot;:false,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;premio&quot;,&quot;label&quot;:&quot;Premio&quot;,&quot;searchable&quot;:false,&quot;sortable&quot;:true,&quot;type&quot;:&quot;currency&quot;}],&quot;groupOrder&quot;:&quot;asc&quot;,&quot;issearchavailable&quot;:false,&quot;issortavailable&quot;:true,&quot;pagelimit&quot;:&quot;0&quot;,&quot;pagesize&quot;:&quot;&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;records&quot;:&quot;{records}&quot;,&quot;searchDatatable&quot;:&quot;&quot;},&quot;size&quot;:{&quot;default&quot;:&quot;12&quot;,&quot;isResponsive&quot;:false},&quot;stateIndex&quot;:0,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;type&quot;:&quot;element&quot;},{&quot;children&quot;:[{&quot;class&quot;:&quot;slds-col &quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;elementLabel&quot;:&quot;Text-2&quot;,&quot;key&quot;:&quot;element_element_block_1_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;property&quot;:{&quot;card&quot;:&quot;{card}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3EVisualizza%20Tutto%3C/div%3E&quot;,&quot;record&quot;:&quot;{record}&quot;},&quot;size&quot;:{&quot;default&quot;:&quot;12&quot;,&quot;isResponsive&quot;:false},&quot;stateIndex&quot;:0,&quot;styleObject&quot;:{&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;},&quot;border&quot;:{&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;,&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;},&quot;class&quot;:&quot;&quot;,&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;inlineStyle&quot;:&quot;color:#0070EF;\ntext-align: center;\nfont-size: 13px;\nfont-style: normal;\nfont-weight: 600;\nline-height: normal;\ntext-decoration-line: underline;\ntext-decoration-style: solid;\ntext-decoration-skip-ink: none;\ntext-decoration-thickness: auto;\ntext-underline-offset: auto;\ntext-underline-position: from-font;&quot;,&quot;margin&quot;:[],&quot;padding&quot;:[],&quot;size&quot;:{&quot;default&quot;:&quot;12&quot;,&quot;isResponsive&quot;:false},&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;style&quot;:&quot;      \n         color:#0070EF;\ntext-align: center;\nfont-size: 13px;\nfont-style: normal;\nfont-weight: 600;\nline-height: normal;\ntext-decoration-line: underline;\ntext-decoration-style: solid;\ntext-decoration-skip-ink: none;\ntext-decoration-thickness: auto;\ntext-underline-offset: auto;\ntext-underline-position: from-font;&quot;,&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;}},&quot;styleObjects&quot;:[{&quot;conditionString&quot;:&quot;&quot;,&quot;conditions&quot;:&quot;default&quot;,&quot;draggable&quot;:false,&quot;key&quot;:0,&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;styleObject&quot;:{&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;},&quot;border&quot;:{&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;,&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;},&quot;class&quot;:&quot;&quot;,&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;inlineStyle&quot;:&quot;color:#0070EF;\ntext-align: center;\nfont-size: 13px;\nfont-style: normal;\nfont-weight: 600;\nline-height: normal;\ntext-decoration-line: underline;\ntext-decoration-style: solid;\ntext-decoration-skip-ink: none;\ntext-decoration-thickness: auto;\ntext-underline-offset: auto;\ntext-underline-position: from-font;&quot;,&quot;margin&quot;:[],&quot;padding&quot;:[],&quot;size&quot;:{&quot;default&quot;:&quot;12&quot;,&quot;isResponsive&quot;:false},&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;style&quot;:&quot;      \n         color:#0070EF;\ntext-align: center;\nfont-size: 13px;\nfont-style: normal;\nfont-weight: 600;\nline-height: normal;\ntext-decoration-line: underline;\ntext-decoration-style: solid;\ntext-decoration-skip-ink: none;\ntext-decoration-thickness: auto;\ntext-underline-offset: auto;\ntext-underline-position: from-font;&quot;,&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;}}}],&quot;type&quot;:&quot;text&quot;}],&quot;class&quot;:&quot;slds-col &quot;,&quot;element&quot;:&quot;block&quot;,&quot;elementLabel&quot;:&quot;Block-2&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;property&quot;:{&quot;action&quot;:{&quot;actionList&quot;:[{&quot;actionIndex&quot;:0,&quot;card&quot;:&quot;{card}&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;key&quot;:&quot;*************-kw779pgo6&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;stateAction&quot;:{&quot;Navigation Item&quot;:{&quot;targetName&quot;:&quot;Titoli_in_scadenza&quot;},&quot;displayName&quot;:&quot;Action&quot;,&quot;hasExtraParams&quot;:true,&quot;id&quot;:&quot;flex-action-*************&quot;,&quot;openUrlIn&quot;:&quot;New Tab/Window&quot;,&quot;targetParams&quot;:{&quot;c__AccountId&quot;:&quot;{recordId}&quot;,&quot;c__UserId&quot;:&quot;{User.userId}&quot;},&quot;targetType&quot;:&quot;Navigation Item&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;}}],&quot;eventType&quot;:&quot;onclick&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;showSpinner&quot;:&quot;false&quot;},&quot;card&quot;:&quot;{card}&quot;,&quot;collapsedByDefault&quot;:false,&quot;collapsible&quot;:false,&quot;label&quot;:&quot;Block&quot;,&quot;record&quot;:&quot;{record}&quot;},&quot;size&quot;:{&quot;default&quot;:&quot;12&quot;,&quot;isResponsive&quot;:false},&quot;stateIndex&quot;:0,&quot;styleObject&quot;:{&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;padding&quot;:[{&quot;size&quot;:&quot;x-small&quot;,&quot;type&quot;:&quot;around&quot;}],&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;type&quot;:&quot;block&quot;}]}},&quot;conditions&quot;:{&quot;group&quot;:[],&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;documents&quot;:[],&quot;fields&quot;:[],&quot;isSmartAction&quot;:false,&quot;name&quot;:&quot;Active&quot;,&quot;omniscripts&quot;:[],&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;},&quot;border&quot;:{&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;,&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;},&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-around_none &quot;,&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;elementStyleProperties&quot;:{},&quot;inlineStyle&quot;:&quot;&quot;,&quot;margin&quot;:[{&quot;label&quot;:&quot;around:none&quot;,&quot;size&quot;:&quot;none&quot;,&quot;type&quot;:&quot;around&quot;}],&quot;padding&quot;:[{&quot;label&quot;:&quot;around:x-small&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;type&quot;:&quot;around&quot;}],&quot;size&quot;:{&quot;default&quot;:&quot;12&quot;,&quot;isResponsive&quot;:false},&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;}}}],&quot;theme&quot;:&quot;slds&quot;,&quot;title&quot;:&quot;UniTitoliInScadenza_Child_DataTable&quot;,&quot;xmlJson&quot;:[{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__AppPage&quot;,&quot;xmlns&quot;:&quot;http://soap.sforce.com/2006/04/metadata&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]},{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__RecordPage&quot;,&quot;xmlns&quot;:&quot;http://soap.sforce.com/2006/04/metadata&quot;},&quot;objects&quot;:{&quot;object&quot;:&quot;Account&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]}],&quot;xmlObject&quot;:{&quot;isExplicitImport&quot;:false,&quot;masterLabel&quot;:&quot;UniTitoliInScadenza_Child_DataTable&quot;,&quot;targetConfigs&quot;:&quot;CiAgICAgICAgPHRhcmdldENvbmZpZyB4bWxucz0iaHR0cDovL3NvYXAuc2ZvcmNlLmNvbS8yMDA2LzA0L21ldGFkYXRhIiB0YXJnZXRzPSJsaWdodG5pbmdfX0FwcFBhZ2UiPgogICAgICA8cHJvcGVydHkgbmFtZT0iZGVidWciIHR5cGU9IkJvb2xlYW4iLz4KICAgICAgPHByb3BlcnR5IG5hbWU9InJlY29yZElkIiB0eXBlPSJTdHJpbmciLz4KICAgIDwvdGFyZ2V0Q29uZmlnPgogICAgPHRhcmdldENvbmZpZyB4bWxucz0iaHR0cDovL3NvYXAuc2ZvcmNlLmNvbS8yMDA2LzA0L21ldGFkYXRhIiB0YXJnZXRzPSJsaWdodG5pbmdfX1JlY29yZFBhZ2UiPgogICAgICA8cHJvcGVydHkgbmFtZT0iZGVidWciIHR5cGU9IkJvb2xlYW4iLz4KICAgICAgPHByb3BlcnR5IG5hbWU9InJlY29yZElkIiB0eXBlPSJTdHJpbmciLz4KICAgICAgPG9iamVjdHM+CiAgICAgICAgPG9iamVjdD5BY2NvdW50PC9vYmplY3Q+CiAgICAgIDwvb2JqZWN0cz4KICAgIDwvdGFyZ2V0Q29uZmlnPgogICAgICAgIA==&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;,&quot;lightning__RecordPage&quot;]}}}</propertySetConfig>
    <stylingConfiguration>{&quot;customStyles&quot;:&quot;.CustomIcon{\r\n    width: 32px;\r\n    height: 32px;\r\n    margin-right: 8px;\r\n}&quot;}</stylingConfiguration>
    <versionNumber>2</versionNumber>
</OmniUiCard>
