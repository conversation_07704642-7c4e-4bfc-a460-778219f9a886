global class FEA_Helper implements Callable {

    private static final List<String> documentTypesToCheck = new List<String>{'CDN', 'CID',
         'LIP', 'PAS', 'PAT', 'POR', 'PSO', 'TES', 'TMR', 'TSS', 'PTN', 'ALB', 'CI', 'D', 'ITA'};
    
    public Object call(String action, Map<String, Object> args) {

        Map<String, Object> input = (Map<String, Object>)args.get('input');
        Map<String, Object> output = (Map<String, Object>)args.get('output');
        Map<String, Object> options = (Map<String, Object>)args.get('options');

        return invokeMethod(action, input, output, options);
    }

    private boolean invokeMethod(String methodName, Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        if (methodName.equals('checkDatiUnivoci')) {
            Fea_Helper.checkDatiUnivoci(inputMap, outMap, options);
        } else if (methodName.equals('checkActiveDocument')) {
            Fea_Helper.checkActiveDocument(inputMap, outMap, options);
        } else if (methodName.equals('createBarcodeAndXML')) {
            Fea_Helper.createBarcodeAndXML(inputMap, outMap, options);
        } else if (methodName.equals('revocaFEA')) {
            Fea_Helper.revocaFEA(inputMap, outMap, options);
        } else if (methodName.equals('certifcazioneContatti')) {
            Fea_Helper.certifcazioneContatti(inputMap, outMap, options);
        } else if (methodName.equals('refreshStatoEmail')) {
            Fea_Helper.refreshStatoEmail(inputMap, outMap, options);
        } else if (methodName.equals('checkAggiorna')) {
            Fea_Helper.checkAggiorna(inputMap, outMap, options);
        } else if (methodName.equals('getDataOdierna')) {
            Fea_Helper.getDataOdierna(inputMap, outMap, options);
        } else if (methodName.equals('getCompanyName')) {
            Fea_Helper.getCompanyName(inputMap, outMap, options);
        } else if (methodName.equals('getUserFiscalCode')) {
            Fea_Helper.getUserFiscalCode(inputMap, outMap, options);
        }
        return true;
    }

    public static void checkDatiUnivoci(Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        System.debug('FEA_Helper - checkDatiUnivoci');
        System.debug('inputMap: ' + inputMap);
        String ciu = inputMap.containsKey('ciu') ? (String) inputMap.get('ciu') : null;
        String email = inputMap.containsKey('email') ? (String) inputMap.get('email') : null;
        String cellulare = inputMap.containsKey('cellulare') ? (String) inputMap.get('cellulare') : null;

        if(ciu != null){
            List<AccountDetails__c> accDetailsList = [SELECT Id, SourceSystemIdentifier__c, FeaMail__c, FeaMobile__c FROM 
                    AccountDetails__c WHERE SourceSystemIdentifier__c =:ciu LIMIT 1];

            if(!accDetailsList.isEmpty()){
                AccountDetails__c accDetails = accDetailsList.get(0);
                String accountDetailsId = accDetails.Id;
                String feaMobile = cellulare;
                String feaMail = email;

                //Pulisco il feaMobile ed aggiungo %
                if (feaMobile.contains('+39')){
                    feaMobile = '%' + feaMobile.replace('+39','') + '%';
                }
                
                //CheckUnivocità
                List<AccountDetails__c> AccDetailsListForCheck = [SELECT Id, FeaMobile__c, FeaMail__c FROM AccountDetails__c 
                        WHERE (FeaMobile__c LIKE :feaMobile  OR FeaMail__c =:feaMail) AND Id != :accountDetailsId]; //Aggiungere controllo sul RecordType?
                if (AccDetailsListForCheck.size() > 0) {
                    System.debug('AccountDetails: ' + AccDetailsListForCheck);
                    //Controllo quale dei due parametri è duplicato
                    Boolean duplicateMailError = false;
                    Boolean duplicateMobileError = false;
                    for(AccountDetails__c accDet : AccDetailsListForCheck){
                        if(accDet.FeaMail__c == feaMail){
                            duplicateMailError = true;
                        }
                        if(accDet.FeaMobile__c == feaMobile){
                            duplicateMobileError = true;
                        }
                    }
                    if(duplicateMailError && duplicateMobileError){
                        outMap.put('status',false);
                        outMap.put('error','Email e Cellulare gia\' in uso per un\'altra FEA');
                    } else if (duplicateMailError && !duplicateMobileError){
                        outMap.put('status',false);
                        outMap.put('error','Email gia\' in uso per un\'altra FEA');
                    } else if (duplicateMobileError && !duplicateMailError){
                        outMap.put('status',false);
                        outMap.put('error','Cellulare gia\' in uso per un\'altra FEA');
                    }
                }
                else outMap.put('status',true);
            }
            else {
                outMap.put('status',false);
                outMap.put('error','AccountDetails not Found');
            }
        }
        else {
            outMap.put('status',false);
            outMap.put('error','Ciu input parameter is null');
        }
    }
    
    public static void checkActiveDocument(Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        System.debug('FEA_Helper - checkActiveDocument');
        System.debug('inputMap: ' + inputMap);
        String ciu = inputMap.containsKey('ciu') ? (String) inputMap.get('ciu') : null;

        List<AccountDetails__c> accDetailsList = [SELECT Id, SourceSystemIdentifier__c, FeaMail__c, FeaMobile__c FROM 
                AccountDetails__c WHERE SourceSystemIdentifier__c =:ciu LIMIT 1];

        if(!accDetailsList.isEmpty()){
            AccountDetails__c accDetails = accDetailsList.get(0);
            List<ContentDocumentLink> contentDocLinkList = [SELECT Id, ContentDocumentId, LinkedEntityId  FROM ContentDocumentLink WHERE LinkedEntityId =:accDetails.Id];
            if(contentDocLinkList.size() > 0){
                List<String> contDocIdList = new List<String>();
                for(ContentDocumentLink cdl : contentDocLinkList){
                    contDocIdList.add(cdl.ContentDocumentId);
                }
                List<ContentVersion> contVerList = [SELECT Id, ExpirationDate__c, RecordType.DeveloperName 
                    FROM ContentVersion WHERE ContentDocumentId IN :contDocIdList]; //Filtro su documentType è da aggiungere?
                for(ContentVersion contVer : contVerList){
                    System.debug('@GP Exp date: ' + contVer.ExpirationDate__c);
                    System.debug('@GP RecordType DevName: ' + contVer.RecordType.DeveloperName);
                    if(contVer.ExpirationDate__c > Date.today() && documentTypesToCheck.contains(contVer.RecordType.DeveloperName)){
                        outMap.put('status',true);
                        System.debug('@GP outMap: ' + outMap);
                        return;
                    }
                }
                outMap.put('status',false);
                outMap.put('error','The Account Details does not have active document');   
            }
            else{
                outMap.put('status',false);
                outMap.put('error','No Document found for this accountDetailsId');
            }    
        }
        else {
            outMap.put('status',false);
            outMap.put('error','AccountDetails not Found');
        }    
    }
    
    public static void createBarcodeAndXML(Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        System.debug('FEA_Helper - createBarcodeAndXML');
        System.debug('inputMap: ' + inputMap);
        String ciu = inputMap.containsKey('ciu') ? (String) inputMap.get('ciu') : null;
        String mobile = inputMap.containsKey('cellulare') ? (String) inputMap.get('cellulare') : null; 
        String email = inputMap.containsKey('email') ? (String) inputMap.get('email') : null;
        String type = inputMap.containsKey('type') ? (String) inputMap.get('type') : null;

        List<AccountDetails__c> accDetailsList = [SELECT Id, Relation__r.FinServ__RelatedAccount__r.ExternalId__c, LastName__c, FirstName__c, Relation__r.FinServ__RelatedAccount__r.Identifier__c,
                Street__c, PostalCode__c, City__c, State__c, Mobile__c, Email__c, FiscalCode__c, SourceSystemConsentCode__c, Relation__r.FinServ__RelatedAccount__r.Name FROM 
                AccountDetails__c WHERE SourceSystemIdentifier__c =:ciu LIMIT 1];
        System.debug('@GP accDetailsList: ' + accDetailsList);

        FEI_Environment__c mySetting = FEI_Environment__c.getInstance();
        FEI_Settings__mdt feiSetting = [SELECT id, UCA_Permission_Name__c FROM FEI_Settings__mdt WHERE Label = 'SFEA.SIGN' AND Environment__c = :mySetting.Environment__c LIMIT 1];

        User activeUser = [SELECT Id, Name, IdAzienda__c, FederationIdentifier FROM User WHERE Id =: userInfo.getUserId() LIMIT 1];        
        System.debug('@GP activeUser.IdAzienda__c: ' + activeUser.IdAzienda__c + ' activeUser.FederationIdentifier: ' + activeUser.FederationIdentifier + ' Society__c: ' + accDetailsList.get(0).Relation__r.FinServ__RelatedAccount__r.ExternalId__c);
        NetworkUser__c networkUser = [SELECT Id, Agency__r.AgencyCode__c, NetworkUser__c FROM NetworkUser__c WHERE IsActive__c = true AND Agency__c =: activeUser.IdAzienda__c 
                AND FiscalCode__c =: activeUser.FederationIdentifier AND Society__c =: accDetailsList.get(0).Relation__r.FinServ__RelatedAccount__r.ExternalId__c LIMIT 1];     
        Account agencyAcc = [SELECT Id, Name, AgencyCode__c FROM Account WHERE Id =: activeUser.IdAzienda__c];
        
        //LOGICA DI COSTRUZIONE DEL PAYLOAD
        if(!accDetailsList.isEmpty()){
            AccountDetails__c accDetails = accDetailsList.get(0);
            String codiceCompagnia = accDetails.Relation__r.FinServ__RelatedAccount__r.ExternalId__c.substringAfter('_');
            String codiceCompagniaForBarcode = accDetails.Relation__r.FinServ__RelatedAccount__r.Identifier__c;         
            String nomeCompagnia = accDetails.Relation__r.FinServ__RelatedAccount__r.Name;
            String fiscalCode = accDetails.FiscalCode__c; 
            String agencyCode = agencyAcc.AgencyCode__c != null ? agencyAcc.AgencyCode__c : '-'; 
            
            //RECUPERO ID DEL DOCUMENTO LOGICA DA RIPRISTINARE - START
            /*List<ContentDocumentLink> contentDocLinkList = [SELECT Id, ContentDocumentId, LinkedEntityId  FROM ContentDocumentLink WHERE LinkedEntityId =:accDetailsList.get(0).Id];
                
            List<String> contDocIdList = new List<String>();
            for(ContentDocumentLink cdl : contentDocLinkList){
                contDocIdList.add(cdl.ContentDocumentId);
            }
            List<ContentVersion> contVerList = [SELECT Id, ExpirationDate__c, RecordType.DeveloperName, SourceSystemIdentifier__c FROM ContentVersion WHERE ContentDocumentId IN :contDocIdList ORDER BY CreatedDate DESC];
            String documentId = !contVerList.isEmpty() ? contVerList.get(0).SourceSystemIdentifier__c : '';
            */
            //RECUPERO ID DEL DOCUMENTO LOGICA DA RIPRISTINARE - END
            
            //RECUPERO ID DEL DOCUMENTO LOGICA WORKAROUND - START
            String documentId = '-';
            Map<String, Object> ipInput = new Map<String, Object>{'ciu' => ciu, 'compagnia' => nomeCompagnia};
            Map<String, Object> ipOutput = (Map<String, Object>) omnistudio.IntegrationProcedureService.runIntegrationService('RestGet_FeaDocumenti', ipInput, null);   
            System.debug('@GP ipOutput: ' + ipOutput);
            List<Object> documentList = (List<Object>)ipOutput.get('results');
            if(!documentList.isEmpty()){
                for(Object document : documentList){
                    Map<String,Object> docMap = (Map<String,Object>)document;
                    if(documentTypesToCheck.contains((String)docMap.get('tipoDelDocumento'))){
                        documentId = String.valueOf(docMap.get('id'));
                        break;
                    }
                    System.debug('@GP Non è stato trovato nessun documento valido');
                }
            }
            System.debug('@GP documentId: ' + documentId);
            //RECUPERO ID DEL DOCUMENTO LOGICA WORKAROUND - START

            String barcode = '';
            DateTime now = DateTime.now();
            if(type.equalsIgnoreCase('FEA')){
                barcode = now.format('yyyyMMddHHmmssSSSSSS'); //BARCODE
            } else if(type.equalsIgnoreCase('FES')){
                barcode = codiceCompagniaForBarcode + fiscalCode + agencyCode + now.format('yyyyMMddHHmmss'); //BARCODE
            } 
            //Creazione Payload XML 
            Map<String,String> companyNameForExtId = new Map <String, String>{'UNIPOL' => 'unipolsai', 'UNISALUTE' => 'unisalute'};
            String xml = '';
            if(type.equalsIgnoreCase('FEA')){
                xml+='<?xml version="1.0" encoding="UTF-8"?>';
                xml+='<form_job>';
                xml+='<form_section>';
                xml+='<form name="UNI_FEA_ADE"/>'; //Sempre vuoto
                xml+='<field name="CONTRAENTE">' + accDetails.LastName__c + ' ' + accDetails.FirstName__c + '</field>';
                xml+='<field name="N_DUS">' + accDetails.Street__c + '</field>';
                xml+='<field name="N_CAP">' + accDetails.PostalCode__c + '</field>';
                xml+='<field name="N_LOCALITA">' + accDetails.City__c + '</field>';
                xml+='<field name="N_PROVINCIA">' + accDetails.State__c + '</field>';
                xml+='<field name="CON_TEL"/>'; //Sempre vuoto
                xml+='<field name="CON_CEL">' + mobile + '</field>'; //Passati dal contesto
                xml+='<field name="CON_MAIL">' + email + '</field>'; //Passati dal contesto
                xml+='<field name="CODFISCONTRA">' + accDetails.FiscalCode__c + '</field>';
                xml+='<field name="RCA_DOC_IDENT"/>'; //Sempre vuoto
                xml+='<field name="CHK_3"/>'; //Sempre vuoto
                xml+='<field name="CHK_4"/>'; //Sempre vuoto
                xml+='<field name="DESC_MEZZO"/>'; //Sempre vuoto
                xml+='<field name="DT_EMISSIONE">13/03/2025</field>'; //TBD - Anche su Excel non è chiaro, per ora lascio stabbata una data a caso
                xml+='<field name="DT_SOTTOSCR">13/03/2025</field>'; //TBD - Anche su Excel non è chiaro, per ora lascio stabbata una data a caso
                xml+='<field name="COD_BARRE">' + barcode + '</field>';
                xml+='<field name="DIVISIONE">' + codiceCompagnia + '</field>'; //Per ora è settata la compagnia, ma bisogna confermare il mapping
                //xml+='<field name="TEST_FEA_UNISIGN2_SERVICE"/>'); //Sembrerebbe un parametro non presente nell'excel, forse è solo di esempio
                xml+='</form_section>';
                xml+='</form_job>';
            }
            else if(type.equalsIgnoreCase('FES')){
                xml+='<?xml version="1.0" encoding="UTF-8"?>';
                xml+='<form_job>';
                xml+='<form_section>';
                xml+='<form name="PRIVACY_CON"/>';
                xml+='<field name="CODFISCONTRA">' + accDetails.FiscalCode__c + '</field>';
                xml+='<field name="ANAGRAFICA">' + accDetails.LastName__c + ' ' + accDetails.FirstName__c + '</field>';
                xml+='<field name="COD_BARRE">' + barcode + '</field>';
                if(accDetails.SourceSystemConsentCode__c == '03'){
                    xml+='<field name="CK_TIP01">S</field>';
                    xml+='<field name="CK_TIP02"/>';
                    xml+='<field name="CK_TIP03"/>';
                }
                if(accDetails.SourceSystemConsentCode__c == '04'){
                    xml+='<field name="CK_TIP01">S</field>';
                    xml+='<field name="CK_TIP02">S</field>';
                    xml+='<field name="CK_TIP03"/>';
                }
                if(accDetails.SourceSystemConsentCode__c == '05'){
                    xml+='<field name="CK_TIP01">S</field>';
                    xml+='<field name="CK_TIP02"/>';
                    xml+='<field name="CK_TIP03">S</field>';
                }
                xml+='<field name="FLAG_PDF"/>';
                xml+='</form_section>';
                xml+='</form_job>';
            }
    

            String finalXml = xml.toString();
            // Conversione della stringa in Blob e codifica Base64
            Blob xmlBlob = Blob.valueOf(finalXml);
            String base64EncodedXml = EncodingUtil.base64Encode(xmlBlob);

            //Costruzione del payload JSON
            Map<String, Object> feasRequest = new Map<String, Object>();
            feasRequest.put('cmp', '1');
            feasRequest.put('prj', 'SF');
            feasRequest.put('app', 'ANAG2');

            // Keys
            Map<String, Object> keys = new Map<String, Object>{
                'COMP' => new Map<String, Object>{ 'value' => '1', 'name' => 'COMP' }, //TBD è sempre 1 perchè lanciamo il processo FEA solo per Unipol?
                'AGEN' => new Map<String, Object>{ 'value' => agencyCode, 'name' => 'AGEN' }, //Mappato
                'CIUID' => new Map<String, Object>{ 'value' => Integer.valueOf(ciu), 'name' => 'CIUID' }, //Mappato il SourceSystemIdentifier__c
                'SUBAG' => new Map<String, Object>{ 'value' => 0, 'name' => 'SUBAG' }, //Fisso a 0
                'DOCECMKEY' => new Map<String, Object>{ 'value' => documentId, 'name' => 'DOCECMKEY' },
                'CODFIS' => new Map<String, Object>{ 'value' => accDetails.FiscalCode__c, 'name' => 'CODFIS' }, //Mappato
                'EMAIL' => new Map<String, Object>{ 'value' => email, 'name' => 'EMAIL' }, //Passati dal contesto
                'CELL' => new Map<String, Object>{ 'value' => mobile, 'name' => 'CELL' } //Passati dal contesto
            };
            feasRequest.put('keys', keys);

            // Descriptors
            Map<String, Object> descriptorKeys = new Map<String, Object>{
                'COMP' => new Map<String, Object>{ 'value' => '1', 'name' => 'COMP' }, //TBD è sempre 1 perchè lanciamo il processo FEA solo per Unipol?
                'CODFIS' => new Map<String, Object>{ 'value' => accDetails.FiscalCode__c, 'name' => 'CODFIS' }, //Mappato
                'TIPDOC' => new Map<String, Object>{ 'value' => 'FEAD', 'name' => 'TIPDOC' }, //Valore fisso 
                'CODDOC' => new Map<String, Object>{ 'value' => barcode, 'name' => 'CODDOC' } //Mappato
            };

            Map<String, Object> descriptor = new Map<String, Object>{
                'descriptor' => base64EncodedXml, //XML Creato prima in Base64
                'type' => 'FEAD', //Valore fisso 
                'keys' => descriptorKeys
            };

            feasRequest.put('descriptors', new List<Object>{ descriptor });

            String cellulareUrl = mobile != null ? mobile.replace('+', '%2B').replaceAll('\\s+', '') : '';
            String emailUrl = email != null ? email.replace('@', '%40') : '';
            String operatorName = !String.isBlank(activeUser.Name) ? activeUser.Name.replace(' ', '%20') : '';
            if(type.equalsIgnoreCase('FES')){
                cellulareUrl = accDetails.Mobile__c != null ? accDetails.Mobile__c.replace('+', '%2B').replaceAll('\\s+', '') : '';
                emailUrl = accDetails.Email__c != null ? accDetails.Email__c.replace('@', '%40') : '';
            }
            String nomeCompagniaForURL = companyNameForExtId.containsKey(nomeCompagnia) ? companyNameForExtId.get(nomeCompagnia) : nomeCompagnia;
           
            //Recupero dell'url
            String host = System.Url.getOrgDomainUrl().getHost(); //Rivedere bene la logica
            String environment;
            if (host.contains('dev')) { 
                environment = 'DEV';
            } else if (host.contains('uat')) {
                environment = 'UAT';
            } else {
                environment = 'PROD';
            }

            urlFeiFEAePrivacy__mdt urlFeiObj = [SELECT Base_URL__c FROM urlFeiFEAePrivacy__mdt WHERE Environment__c = :environment LIMIT 1];
            System.debug('@GP BaseUrl: ' + urlFeiObj.Base_URL__c);
           
            // Altri campi
            if(type.equalsIgnoreCase('FEA')){
                //@GP modifica parametri url 18/07/25 - Per Test con Reply
                feasRequest.put('srcAppBckUrl', urlFeiObj.Base_URL__c + '/api/v1/fea/callback/feaSign?res=OK&type=FEA&ciu=' + ciu + '&reservedAreaFlag=true&company=' + codiceCompagnia + '&companyName=' + nomeCompagniaForURL + '&mobile=' + cellulareUrl + '&email=' + emailUrl + '&operatorId=' + networkUser.NetworkUser__c + '&operatorName=' + operatorName + '&barcode=' + barcode + '&codiceAgenzia=' + agencyCode + '&tipoVersioneFEA=2#get');
                feasRequest.put('srcAppFailBckUrl', urlFeiObj.Base_URL__c + '/api/v1/fea/callback/feaSign?res=KO&type=FEA&ciu=' + ciu + '&reservedAreaFlag=false&company=' + codiceCompagnia + '&companyName=' + nomeCompagniaForURL + '&operatorId=' + networkUser.NetworkUser__c + '&operatorName=' + operatorName + '#get');
                feasRequest.put('mnuAppBckUrl', 'MENU#get');
                
                /*feasRequest.put('srcAppBckUrl', 'https://essig-inte.unipolsai.it');
                feasRequest.put('srcAppFailBckUrl', 'https://essig-inte.unipolsai.it');
                feasRequest.put('mnuAppBckUrl', 'https://essig-inte.unipolsai.it');*/
            } else if(type.equalsIgnoreCase('FES')){
                feasRequest.put('srcAppBckUrl', urlFeiObj.Base_URL__c + '/api/v1/fea/callback/feaSign?codiceFiscale=' + accDetails.FiscalCode__c + '&clientType=PF&subjectType=D&res=OK&type=PRIVACY&ciu=' + ciu + '&reservedAreaFlag=false&company=' + codiceCompagnia + '&companyName=' + nomeCompagniaForURL + '&mobile=' + cellulareUrl + '&email=' + emailUrl + '&operatorId=' + networkUser.NetworkUser__c + '&operatorName=' + operatorName + '&codiceAgenzia=' + agencyCode + '&barcode=' + barcode + '#get');
                feasRequest.put('srcAppFailBckUrl', urlFeiObj.Base_URL__c +  '/api/v1/fea/callback/feaSign?res=KO&type=PRIVACY&ciu=' + ciu + '&reservedAreaFlag=false&company=' + codiceCompagnia + '&companyName=' + nomeCompagniaForURL + '&mobile=' + cellulareUrl + '&email=' + emailUrl + '&operatorId=' + networkUser.NetworkUser__c + '&operatorName=' + operatorName + '#get'); 
                feasRequest.put('mnuAppBckUrl', 'MENU#get');
            }
            feasRequest.put('finalPrint', true);
            feasRequest.put('sendMail', true);
            feasRequest.put('sendSms', true);
            feasRequest.put('saveStatus', false);
            feasRequest.put('sendMailPaper', false);
            feasRequest.put('signTyp', 'A'); //Sempre questo valore  
            feasRequest.put('contactTyp', 'F'); //Sempre questo valore  
            feasRequest.put('channelTyp', 'AGZ'); //Sempre questo valore  
            feasRequest.put('sendEcm', true);
            feasRequest.put('sendCsst', true);
            feasRequest.put('sendPec', false);

            // Wrappa tutto
            Map<String, Object> root = new Map<String, Object>{
                'feasRequest' => feasRequest
            };

            // Serializza in JSON
            String jsonOutput = JSON.serialize(root);
            outMap.put('feiRequest',jsonOutput);
            outMap.put('permissionSet',feiSetting.UCA_Permission_Name__c);
            outMap.put('status',true);
            outMap.put('userFiscalCode',activeUser.FederationIdentifier);
            System.debug('@GP payload: ' + jsonOutput);
            System.debug('@GP outMap: ' + outMap);
        }  
        else {
            outMap.put('status',false);
            outMap.put('error','AccountDetails not Found');
        } 
    }   

    public static void revocaFEA (Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        System.debug('@GP revocaFEA inputMap: ' + inputMap);
        //INPUT Della GETFEA
        String ciu = inputMap.containsKey('ciu') ? String.valueOf(inputMap.get('ciu')) : null;
        String accountId = inputMap.containsKey('accountId') ? String.valueOf(inputMap.get('accountId')) : null;
        String feaId = inputMap.containsKey('feaId') ? String.valueOf(inputMap.get('feaId')) : null;
        String compagnia = inputMap.containsKey('compagnia') ? (String) inputMap.get('compagnia') : null;
        //INPUT Della ContattoFea della GETFEA
        String idContattoFea = inputMap.containsKey('idContattoFea') ? String.valueOf(inputMap.get('idContattoFea')) : null;
        String email = inputMap.containsKey('email') ? (String) inputMap.get('email') : null;
        String cellulare = inputMap.containsKey('cellulare') ? (String) inputMap.get('cellulare') : null;
        String statoValidazioneMail = inputMap.containsKey('statoValidazioneMail') ? (String) inputMap.get('statoValidazioneMail') : null;

        List<AccountDetails__c> accDetailsList = [SELECT Id, Name, FiscalCode__c, FeaVersionType__c, FeaEffectiveDate__c, FeaEndDate__c, Relation__r.FinServ__RelatedAccount__r.ExternalId__c FROM 
                AccountDetails__c WHERE SourceSystemIdentifier__c =:ciu LIMIT 1];
        
        User activeUser = [SELECT Id, Name, IdAzienda__c, FederationIdentifier FROM User WHERE Id =: userInfo.getUserId() LIMIT 1];     
        Account agency = [SELECT Id, AgencyCode__c FROM Account WHERE Id =:activeUser.IdAzienda__c LIMIT 1];

        String companyCode = accDetailsList.get(0).Relation__r.FinServ__RelatedAccount__r.ExternalId__c.substringAfter('_');   
        
        //CHECK PRELIMINARI PER LA REVOCA
        //CHECK PRESENZA CONTATTI DI AREA RISERVATA
        Boolean checkContatti = false;
        Boolean checkPresenzaSoggetto = false;
        List<AccountDetails__c> accDetailsListForCheckContatti = [SELECT Id, FiscalCode__c, Mobile__c, Email__c, OtherMobile__c, OtherEmail__c FROM 
                AccountDetails__c WHERE SourceSystemIdentifier__c =:ciu AND RecordType.DeveloperName IN ('PA','MDM')];    
        System.debug('@GP revocaFEA accDetailsListForCheckContatti: ' + accDetailsListForCheckContatti);
        for(AccountDetails__c accDetails : accDetailsListForCheckContatti){
            if(accDetails.RecordType.DeveloperName == 'PA' && accDetails.Mobile__c != null && accDetails.Email__c != null){
                checkContatti = true;
            } else if(accDetails.RecordType.DeveloperName == 'MDM' && accDetails.OtherMobile__c != null && accDetails.OtherEmail__c != null){
                checkContatti = true;
            }  
        }
        //CHECK PRESENZA SOGGETTO
        if(checkContatti != true){ 
            List<Asset> assetList = [SELECT Value__c FROM Asset WHERE MasterRecordId__r.FinServ__Account__c = :accountId AND MasterRecordId__r.FinServ__RelatedAccount__r.ExternalId__c = 'SOC_1' AND Key__c = 'TPD_DATAULTIMOACCESSO'];
            if(!assetList.isEmpty()) checkPresenzaSoggetto = true;
        }
        System.debug('@GP revocaFEA checkContatti: ' + checkContatti);
        System.debug('@GP revocaFEA checkPresenzaSoggetto: ' + checkPresenzaSoggetto);
        if(!checkContatti && !checkPresenzaSoggetto){ 
            //CHECK FRAZIONAMENTI
            Map<String,Object> ipInput = new Map<String,Object>();
            Map<String,Object> frazionamentiOutput = new Map<String,Object>();

            ipInput.put('codiceFiscale', accDetailsList.get(0).FiscalCode__c);
            ipInput.put('compagnia', '1'); //Al momento inserisco Unipol
            frazionamentiOutput = AddressService.invokeIntegrationProcedureLookup('RestGet_FeaAssociazioneStrumenti', ipInput, null);
            List<Object> fraz = (List<Object>)frazionamentiOutput.get('associazioni');
            if(fraz != null && !fraz.isEmpty()){ 
                outMap.put('status',false);
                outMap.put('error','Utente non censito a sistema e con Frazionamenti attivi');
                return;
            } 
        }
         
        //COSTRUZIONE PAYLOAD DI REVOCA
        if(!accDetailsList.isEmpty()){
            AccountDetails__c accDetails = accDetailsList.get(0);
            Map<String, Object> payload = new Map<String, Object>();

            payload.put('compagnia', compagnia);
            payload.put('dataFineEffetto', Date.today());
            payload.put('tipoVersioneFea', Integer.valueOf(accDetails.FeaVersionType__c));
            payload.put('codiceFiscale', accDetails.FiscalCode__c);
            payload.put('codiceCompagnia', companyCode);
            payload.put('codiceAgenzia', agency.AgencyCode__c);
            payload.put('nominativoSoggetto', accDetails.Name);

            // Dati Contatto
            Map<String, Object> datiContatto = new Map<String, Object>();
            datiContatto.put('cellulare', Long.valueOf(cellulare));
            datiContatto.put('ciu', Long.valueOf(ciu));
            datiContatto.put('email', email);
            payload.put('datiContatto', datiContatto);

            // Dati Tracciatura
            Map<String, Object> datiTracciatura = new Map<String, Object>();
            datiTracciatura.put('compagniaAggiornamento', compagnia);
            //datiTracciatura.put('userId', 'Empty');
            //datiTracciatura.put('username', activeUser.Name);
            payload.put('datiTracciatura', datiTracciatura);


            // Serializzazione in JSON
            String requestPayload = JSON.serialize(payload);
            System.debug('@GP requestPayload: ' + requestPayload);
            outMap.put('requestPayload',requestPayload);
            outMap.put('activeUserId',activeUser.Id);
            outMap.put('accountDetailsId',accDetailsList.get(0).Id);
            outMap.put('status',true);
            System.debug('@GP requestPayload: ' + outMap);
        }
        else {
            outMap.put('status',false);
            outMap.put('error','AccountDetails not Found');
        }
    }

    public static void certifcazioneContatti (Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        //Input Parameters:
        String email = inputMap.containsKey('email') ? (String) inputMap.get('email') : null;
        String cellulare = inputMap.containsKey('cellulare') ? (String) inputMap.get('cellulare') : null;
        String ciu = inputMap.containsKey('ciu') ? String.valueOf(inputMap.get('ciu')) : null;
        
        List<AccountDetails__c> accDetailsList = [SELECT Id, LastName__c, FirstName__c, FiscalCode__c FROM 
                AccountDetails__c WHERE SourceSystemIdentifier__c =:ciu LIMIT 1];

        if(!accDetailsList.isEmpty()){
            AccountDetails__c accDetails = accDetailsList.get(0);
            Map<String, Object> payload = new Map<String, Object>{
                'actionScope' => 'ANAG', 
                'canale' => 'EMAIL', 
                'cfpivacliente' => accDetails.FiscalCode__c, //CodiceFiscale o Partita Iva
                'correlationId' => generateUUID(), //uuid, isolato sulla chiamata
                'correlationType' => 'FEA', 
                'deliverySystem' => 'HERMES', 
                'durata' => ********,
                'email' => email, //Email che si vuole certificare
                'emailType' => 'principale', 
                'from' => '<EMAIL>', //Sempre questa mail?
                'linkConferma' => 'Y', 
                'linkNonConferma' => 'Y', 
                'nomeCompleto' => accDetails.FirstName__c + ' ' + accDetails.LastName__c, //nome cliente che appare nella mail (FirstName + LastName)
                'sistema' => 'SFDC', 
                'telefono' => 'string' //se certificazione mail non obbligatorio, ma per FEA obbligatorio (quello della maschera FEA). Da passare con il +39
                };
            
            String requestPayload = JSON.serialize(payload);
            System.debug('@GP requestPayload: ' + requestPayload);
            outMap.put('requestPayload',requestPayload);
            outMap.put('status',true);
        }
        else {
            outMap.put('status',false);
            outMap.put('error','AccountDetails not Found');
        }
    }

    public static void refreshStatoEmail (Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        String ciu = inputMap.containsKey('ciu') ? String.valueOf(inputMap.get('ciu')) : null;
        
        List<AccountDetails__c> accDetailsList = [SELECT Id, EmailStatus__c FROM 
                AccountDetails__c WHERE SourceSystemIdentifier__c =:ciu LIMIT 1];

        if(!accDetailsList.isEmpty()){
            AccountDetails__c accDetails = accDetailsList.get(0);
            if(accDetails.EmailStatus__c.equalsIgnoreCase('Certified')){
                outMap.put('status',true);
            }
            else {
                outMap.put('status',false);
            }
        }
        else {
            outMap.put('status',false);
            outMap.put('error','AccountDetails not Found');
        }
    }

    public static void checkAggiorna (Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        String ciu = inputMap.containsKey('ciu') ? String.valueOf(inputMap.get('ciu')) : null;
        
        List<AccountDetails__c> accDetailsList = [SELECT Id, FEA__c, FeaEffectiveDate__c, FeaEndDate__c, FeaVersionType__c FROM 
                AccountDetails__c WHERE SourceSystemIdentifier__c =:ciu LIMIT 1];
        
        if(!accDetailsList.isEmpty()){
            //CONTROLLO SU AGGIORNAMENTO TOPIC
            AccountDetails__c accDetails = accDetailsList.get(0);
            if(accDetails.FeaVersionType__c != null && accDetails.FeaVersionType__c != '0'){
                outMap.put('status',true);
            }
            else {
                outMap.put('status',false);
                outMap.put('error','FEA still not updated');
            }
        }
        else {
            outMap.put('status',false);
            outMap.put('error','AccountDetails not Found');
        }
    }

    public static void getDataOdierna (Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        DateTime now = DateTime.now();
        String dateTimeOdierno = now.format('yyyy-MM-dd\'T\'HH:mm:ss');
        outMap.put('dateTimeOdierno',dateTimeOdierno);
    }

    public static void getCompanyName (Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        String company = inputMap.containsKey('company') ? String.valueOf(inputMap.get('company')) : null;
        
        Map<String,String> companyNameForExtId = new Map <String, String>{'SOC_1' => 'unipolsai', 'SOC_4' => 'unisalute'};
        List<Account> comapanyList = [SELECT Id, ExternalId__c from account where name = :company LIMIT 1];
        Account companyAcc = !comapanyList.isEmpty() ? comapanyList.get(0) : null;
        if(companyAcc != null){
            outMap.put('companyName', companyNameForExtId.get(companyAcc.ExternalId__c));
        }
        else outMap.put('companyName',company); 
    }

    public static void getUserFiscalCode (Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        User activeUser = [SELECT Id, Name, IdAzienda__c, FederationIdentifier FROM User WHERE Id =: userInfo.getUserId() LIMIT 1];    
        outMap.put('userFiscalCode',activeUser.FederationIdentifier);    
    }

    /*public static void popolamentoDatiIniziale (Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        String ciu = inputMap.containsKey('ciu') ? String.valueOf(inputMap.get('ciu')) : null;

        String cellulare = '';
        String email = '';

        List<AccountDetails__c> accDetailsList = [SELECT Id, FeaMail__c, FeaMobile__c, Email__c, Mobile__c, OtherEmail__c OtherMobile__c, RecordType.DeveloperName FROM 
                AccountDetails__c WHERE SourceSystemIdentifier__c =:ciu LIMIT 1];
        
        if(!accDetailsList.isEmpty()){
            AccountDetails__c accDetails = accDetailsList.get(0);
            if(accDetails.FeaMail__c != null){
                email = accDetails.FeaMail__c;
            }
            if(accDetails.FeaMobile__c != null){
                cellulare = accDetails.FeaMobile__c;
            }   
            if(String.isBlank(email) && accDetails.RecordType.DeveloperName == 'PA'){
                email = String.isBlank(accDetails.Email__c) ? accDetails.Email__c : '';
            }
            if(String.isBlank(cellulare) && accDetails.RecordType.DeveloperName == 'PA'){
                cellulare = String.isBlank(accDetails.Mobile__c) ? accDetails.Mobile__c : '';
            }
            if(String.isBlank(email) && accDetails.RecordType.DeveloperName == 'MDM'){
                email = String.isBlank(accDetails.OtherEmail__c) ? accDetails.OtherEmail__c : '';
            }
            if(String.isBlank(cellulare) && accDetails.RecordType.DeveloperName == 'MDM'){
                cellulare = String.isBlank(accDetails.OtherMobile__c) ? accDetails.OtherMobile__c : '';
            }  
        }
        outMap.put('email',email);
        outMap.put('cellulare',cellulare);
    }*/


    //METODI A SUPPORTO DELLA CLASSE 
    
    private static String generateUUID() {
        
        Long timestamp = DateTime.now().getTime();
        Long random1 = Crypto.getRandomLong();
        Long random2 = Crypto.getRandomLong();

        String raw = timestamp + '-' + Math.abs(random1) + '-' + Math.abs(random2);
        Blob hash = Crypto.generateDigest('SHA-256', Blob.valueOf(raw));
        String hex = EncodingUtil.convertToHex(hash);

        // Formattazione UUID: 8-4-4-4-12
        String uuid = hex.substring(0, 8) + '-' +
        hex.substring(8, 12) + '-' +
        hex.substring(12, 16) + '-' +
        hex.substring(16, 20) + '-' +
        hex.substring(20, 32);
        return uuid;
    }
    
}