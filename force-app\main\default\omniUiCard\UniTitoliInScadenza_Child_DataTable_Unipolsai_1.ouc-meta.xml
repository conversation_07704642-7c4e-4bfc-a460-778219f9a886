<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <clonedFromOmniUiCardKey>SS_Task_In_Scadenza_Test/Unipolsai/1.0</clonedFromOmniUiCardKey>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:null,&quot;value&quot;:{},&quot;orderBy&quot;:{},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;0019O00000rv4A2QAI&quot;,&quot;id&quot;:1}]},&quot;state0element1_0&quot;:{&quot;type&quot;:null,&quot;value&quot;:{&quot;bundleType&quot;:&quot;Extract&quot;},&quot;orderBy&quot;:{},&quot;contextVariables&quot;:[]}}</dataSourceConfig>
    <description>New Titoli in scadenza child Flexcard to manage the DataTable</description>
    <isActive>false</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>UniTitoliInScadenza_Child_DataTable</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;none&quot;,&quot;label&quot;:&quot;around:none&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-around_none &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;blankCardState&quot;:false,&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Datatable&quot;,&quot;element&quot;:&quot;flexDatatable&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;issearchavailable&quot;:false,&quot;issortavailable&quot;:true,&quot;cellLevelEdit&quot;:true,&quot;pagelimit&quot;:&quot;0&quot;,&quot;groupOrder&quot;:&quot;asc&quot;,&quot;searchDatatable&quot;:&quot;&quot;,&quot;pagesize&quot;:&quot;&quot;,&quot;columns&quot;:[{&quot;fieldName&quot;:&quot;compagnia&quot;,&quot;label&quot;:&quot;Società&quot;,&quot;searchable&quot;:false,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;tipo&quot;,&quot;label&quot;:&quot;Tipo&quot;,&quot;searchable&quot;:false,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;ambito&quot;,&quot;label&quot;:&quot;Ambito&quot;,&quot;searchable&quot;:false,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;polizza&quot;,&quot;label&quot;:&quot;Numero Polizza/Posizione&quot;,&quot;searchable&quot;:false,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;dataScadenzaTitolo&quot;,&quot;label&quot;:&quot;Scadenza&quot;,&quot;searchable&quot;:false,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;totale&quot;,&quot;label&quot;:&quot;Premio&quot;,&quot;searchable&quot;:false,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;}],&quot;records&quot;:&quot;{records}&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Datatable-1&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onclick&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1748528290439-kw779pgo6&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-*************&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Navigation Item&quot;,&quot;openUrlIn&quot;:&quot;New Tab/Window&quot;,&quot;Navigation Item&quot;:{&quot;targetName&quot;:&quot;Titoli_in_scadenza&quot;},&quot;hasExtraParams&quot;:true,&quot;targetParams&quot;:{&quot;c__AccountId&quot;:&quot;{recordId}&quot;,&quot;c__UserId&quot;:&quot;{User.userId}&quot;}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3EVisualizza%20Tutto%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;color:#0070EF;\ntext-align: center;\nfont-size: 13px;\nfont-style: normal;\nfont-weight: 600;\nline-height: normal;\ntext-decoration-line: underline;\ntext-decoration-style: solid;\ntext-decoration-skip-ink: none;\ntext-decoration-thickness: auto;\ntext-underline-offset: auto;\ntext-underline-position: from-font;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         color:#0070EF;\ntext-align: center;\nfont-size: 13px;\nfont-style: normal;\nfont-weight: 600;\nline-height: normal;\ntext-decoration-line: underline;\ntext-decoration-style: solid;\ntext-decoration-skip-ink: none;\ntext-decoration-thickness: auto;\ntext-underline-offset: auto;\ntext-underline-position: from-font;&quot;},&quot;elementLabel&quot;:&quot;Text-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;color:#0070EF;\ntext-align: center;\nfont-size: 13px;\nfont-style: normal;\nfont-weight: 600;\nline-height: normal;\ntext-decoration-line: underline;\ntext-decoration-style: solid;\ntext-decoration-skip-ink: none;\ntext-decoration-thickness: auto;\ntext-underline-offset: auto;\ntext-underline-position: from-font;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         color:#0070EF;\ntext-align: center;\nfont-size: 13px;\nfont-style: normal;\nfont-weight: 600;\nline-height: normal;\ntext-decoration-line: underline;\ntext-decoration-style: solid;\ntext-decoration-skip-ink: none;\ntext-decoration-thickness: auto;\ntext-underline-offset: auto;\ntext-underline-position: from-font;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_1_0_outputField_0_0&quot;}],&quot;elementLabel&quot;:&quot;Block-2&quot;}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:null,&quot;value&quot;:{},&quot;orderBy&quot;:{},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;0019O00000rv4A2QAI&quot;,&quot;id&quot;:1}]},&quot;title&quot;:&quot;UniTitoliInScadenza_Child_DataTable&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;xmlObject&quot;:{&quot;isExplicitImport&quot;:false,&quot;targetConfigs&quot;:&quot;CiAgICAgICAgPHRhcmdldENvbmZpZyB4bWxucz0iaHR0cDovL3NvYXAuc2ZvcmNlLmNvbS8yMDA2LzA0L21ldGFkYXRhIiB0YXJnZXRzPSJsaWdodG5pbmdfX0FwcFBhZ2UiPgogICAgICA8cHJvcGVydHkgbmFtZT0iZGVidWciIHR5cGU9IkJvb2xlYW4iLz4KICAgICAgPHByb3BlcnR5IG5hbWU9InJlY29yZElkIiB0eXBlPSJTdHJpbmciLz4KICAgIDwvdGFyZ2V0Q29uZmlnPgogICAgPHRhcmdldENvbmZpZyB4bWxucz0iaHR0cDovL3NvYXAuc2ZvcmNlLmNvbS8yMDA2LzA0L21ldGFkYXRhIiB0YXJnZXRzPSJsaWdodG5pbmdfX1JlY29yZFBhZ2UiPgogICAgICA8cHJvcGVydHkgbmFtZT0iZGVidWciIHR5cGU9IkJvb2xlYW4iLz4KICAgICAgPHByb3BlcnR5IG5hbWU9InJlY29yZElkIiB0eXBlPSJTdHJpbmciLz4KICAgICAgPG9iamVjdHM+CiAgICAgICAgPG9iamVjdD5BY2NvdW50PC9vYmplY3Q+CiAgICAgIDwvb2JqZWN0cz4KICAgIDwvdGFyZ2V0Q29uZmlnPgogICAgICAgIA==&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;,&quot;lightning__RecordPage&quot;]},&quot;masterLabel&quot;:&quot;UniTitoliInScadenza_Child_DataTable&quot;},&quot;isRepeatable&quot;:false,&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfUniTitoliInScadenza_Child_DataTable_1_Unipolsai&quot;,&quot;Id&quot;:&quot;0Rb9O000003Es6rSAC&quot;,&quot;MasterLabel&quot;:&quot;cfUniTitoliInScadenza_Child_DataTable_1_Unipolsai&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;},&quot;xmlJson&quot;:[{&quot;@attributes&quot;:{&quot;xmlns&quot;:&quot;http://soap.sforce.com/2006/04/metadata&quot;,&quot;targets&quot;:&quot;lightning__AppPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]},{&quot;@attributes&quot;:{&quot;xmlns&quot;:&quot;http://soap.sforce.com/2006/04/metadata&quot;,&quot;targets&quot;:&quot;lightning__RecordPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}],&quot;objects&quot;:{&quot;object&quot;:&quot;Account&quot;}}],&quot;dynamicCanvasWidth&quot;:{&quot;type&quot;:&quot;desktop&quot;},&quot;globalCSS&quot;:false,&quot;selectedCardsLabel&quot;:&quot;DettaglioTitoliScadenza&quot;}</propertySetConfig>
    <stylingConfiguration>{&quot;customStyles&quot;:&quot;.CustomIcon{\r\n    width: 32px;\r\n    height: 32px;\r\n    margin-right: 8px;\r\n}&quot;}</stylingConfiguration>
    <versionNumber>1</versionNumber>
</OmniUiCard>
