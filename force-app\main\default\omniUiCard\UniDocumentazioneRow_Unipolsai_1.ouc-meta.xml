<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <clonedFromOmniUiCardKey>flyoutRow/Unipolsai/2.0</clonedFromOmniUiCardKey>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;Custom&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;resultVar&quot;:&quot;[\&quot;documentazione\&quot;]&quot;,&quot;body&quot;:&quot;{\n  \&quot;documentazioneCount\&quot;:3,\n  \&quot;documentazione\&quot;: [\n    {\n      \&quot;societa\&quot;: \&quot;Usai\&quot;,\n      \&quot;tipologia\&quot;: \&quot;Carta Identità\&quot;,\n      \&quot;numero_documento\&quot;: \&quot;Documento 1\&quot;,\n      \&quot;data_scadenza\&quot;: \&quot;04/10/2024\&quot;\n    },\n    {\n      \&quot;societa\&quot;: \&quot;Usalute\&quot;,\n      \&quot;tipologia\&quot;: \&quot;Patente\&quot;,\n      \&quot;numero_documento\&quot;: \&quot;Documento 2\&quot;,\n      \&quot;data_scadenza\&quot;: \&quot;02/10/2024\&quot;\n    },\n    {\n      \&quot;societa\&quot;: \&quot;Utech\&quot;,\n      \&quot;tipologia\&quot;: \&quot;Tessera convenzione\&quot;,\n      \&quot;numero_documento\&quot;: \&quot;Documento 3\&quot;,\n      \&quot;data_scadenza\&quot;: \&quot;02/10/2024\&quot;\n    }\n  ]\n}\n&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[]}}</dataSourceConfig>
    <isActive>false</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>UniDocumentazioneRow</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;none&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;     border-bottom: #e5e5e5 1px solid; \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_0_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%7Bsocieta%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_2-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Text-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_2-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;key&quot;:&quot;element_element_block_0_0_outputField_1_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;4&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%7Btipologia%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;4&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_4-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Text-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;4&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_4-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;3&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%7Bnumero_documento%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;3&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_3-of-12 slds-medium-size_3-of-12 slds-small-size_3-of-12 slds-size_3-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Text-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;3&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_3-of-12 slds-medium-size_3-of-12 slds-small-size_3-of-12 slds-size_3-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_0_0_outputField_2_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%7Bdata_scadenza%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_2-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;max-width:70px;\nmin-width:70px;\n&quot;,&quot;class&quot;:&quot;slds-theme_default slds-text-align_right  &quot;,&quot;style&quot;:&quot;      \n         max-width:70px;\nmin-width:70px;\n&quot;,&quot;theme&quot;:&quot;theme_default&quot;},&quot;elementLabel&quot;:&quot;Block-1-Text-3&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_2-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;max-width:70px;\nmin-width:70px;\n&quot;,&quot;class&quot;:&quot;slds-theme_default slds-text-align_right  &quot;,&quot;style&quot;:&quot;      \n         max-width:70px;\nmin-width:70px;\n&quot;,&quot;theme&quot;:&quot;theme_default&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_0_0_outputField_3_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;},{&quot;name&quot;:&quot;Menu&quot;,&quot;element&quot;:&quot;flexMenu&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;type&quot;:&quot;action&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;variant&quot;:&quot;&quot;,&quot;iconName&quot;:&quot;utility:down&quot;,&quot;overflow&quot;:true,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;menuItems&quot;:[{&quot;name&quot;:&quot;menu-item-1743177862039-0&quot;,&quot;label&quot;:&quot;Scarica Documento&quot;,&quot;iconName&quot;:&quot;&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1743177926683-o0yo2loow&quot;,&quot;label&quot;:&quot;Scarica Documento&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;test-action&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;}}}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;iconPosition&quot;:&quot;&quot;},{&quot;name&quot;:&quot;menu-item-1743413158864-0&quot;,&quot;label&quot;:&quot;Storico Documento&quot;,&quot;iconName&quot;:&quot;&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1743413158877-5bsh0eczd&quot;,&quot;label&quot;:&quot;Storico Documento&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1752591254862&quot;,&quot;type&quot;:&quot;Flyout&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;flyoutType&quot;:&quot;childCard&quot;,&quot;openFlyoutIn&quot;:&quot;Modal&quot;,&quot;channelName&quot;:&quot;&quot;,&quot;cardName&quot;:&quot;childflyout&quot;,&quot;flyoutLwc&quot;:&quot;childflyout&quot;,&quot;cardNode&quot;:&quot;{record}&quot;,&quot;hasExtraParams&quot;:true,&quot;flyoutParams&quot;:{&quot;societa&quot;:&quot;{societa}&quot;,&quot;tipologia&quot;:&quot;{tipologia}&quot;,&quot;numero_documento&quot;:&quot;{numero_documento}&quot;,&quot;data_scadenza&quot;:&quot;{data_scadenza}&quot;}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;iconPosition&quot;:&quot;&quot;},{&quot;name&quot;:&quot;menu-item-1743413227700-0&quot;,&quot;label&quot;:&quot;Modifica&quot;,&quot;iconName&quot;:&quot;&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1743413227718-6w44vbt6n&quot;,&quot;label&quot;:&quot;Modifica&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;test-action&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;}}}],&quot;showSpinner&quot;:&quot;false&quot;},{&quot;name&quot;:&quot;menu-item-1743413256473-0&quot;,&quot;label&quot;:&quot;Elimina&quot;,&quot;iconName&quot;:&quot;&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1743413256485-tdc8igpp6&quot;,&quot;label&quot;:&quot;Elimina&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;test-action&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;}}}],&quot;showSpinner&quot;:&quot;false&quot;}],&quot;position&quot;:&quot;right&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;max-width:71px;\nmin-width: 71px;\n&quot;,&quot;class&quot;:&quot;slds-theme_default slds-text-align_right &quot;,&quot;style&quot;:&quot;      \n         max-width:71px;\nmin-width: 71px;\n&quot;,&quot;theme&quot;:&quot;theme_default&quot;},&quot;elementLabel&quot;:&quot;Menu-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;max-width:71px;\nmin-width: 71px;\n&quot;,&quot;class&quot;:&quot;slds-theme_default slds-text-align_right &quot;,&quot;style&quot;:&quot;      \n         max-width:71px;\nmin-width: 71px;\n&quot;,&quot;theme&quot;:&quot;theme_default&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_0_0_flexMenu_4_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;}],&quot;elementLabel&quot;:&quot;Block-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;     border-bottom: #e5e5e5 1px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;Custom&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;resultVar&quot;:&quot;[\&quot;documentazione\&quot;]&quot;,&quot;body&quot;:&quot;{\n  \&quot;documentazioneCount\&quot;:3,\n  \&quot;documentazione\&quot;: [\n    {\n      \&quot;societa\&quot;: \&quot;Usai\&quot;,\n      \&quot;tipologia\&quot;: \&quot;Carta Identità\&quot;,\n      \&quot;numero_documento\&quot;: \&quot;Documento 1\&quot;,\n      \&quot;data_scadenza\&quot;: \&quot;04/10/2024\&quot;\n    },\n    {\n      \&quot;societa\&quot;: \&quot;Usalute\&quot;,\n      \&quot;tipologia\&quot;: \&quot;Patente\&quot;,\n      \&quot;numero_documento\&quot;: \&quot;Documento 2\&quot;,\n      \&quot;data_scadenza\&quot;: \&quot;02/10/2024\&quot;\n    },\n    {\n      \&quot;societa\&quot;: \&quot;Utech\&quot;,\n      \&quot;tipologia\&quot;: \&quot;Tessera convenzione\&quot;,\n      \&quot;numero_documento\&quot;: \&quot;Documento 3\&quot;,\n      \&quot;data_scadenza\&quot;: \&quot;02/10/2024\&quot;\n    }\n  ]\n}\n&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[]},&quot;title&quot;:&quot;UniDocumentazioneRow&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfConfiguratoreProfiliRow_1_Unipolsai&quot;,&quot;Id&quot;:&quot;0Rb9O000003ASu9SAG&quot;,&quot;MasterLabel&quot;:&quot;cfConfiguratoreProfiliRow_1_Unipolsai&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;},&quot;xmlObject&quot;:{&quot;masterLabel&quot;:&quot;UniDocumentazioneRow&quot;,&quot;isExposed&quot;:true,&quot;apiVersion&quot;:56}}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;documentazioneCount&quot;:3,&quot;documentazione&quot;:[{&quot;societa&quot;:&quot;Usai&quot;,&quot;tipologia&quot;:&quot;Carta Identità&quot;,&quot;numero_documento&quot;:&quot;Documento 1&quot;,&quot;data_scadenza&quot;:&quot;04/10/2024&quot;},{&quot;societa&quot;:&quot;Usalute&quot;,&quot;tipologia&quot;:&quot;Patente&quot;,&quot;numero_documento&quot;:&quot;Documento 2&quot;,&quot;data_scadenza&quot;:&quot;02/10/2024&quot;},{&quot;societa&quot;:&quot;Utech&quot;,&quot;tipologia&quot;:&quot;Tessera convenzione&quot;,&quot;numero_documento&quot;:&quot;Documento 3&quot;,&quot;data_scadenza&quot;:&quot;02/10/2024&quot;}]}</sampleDataSourceResponse>
    <versionNumber>1</versionNumber>
</OmniUiCard>
