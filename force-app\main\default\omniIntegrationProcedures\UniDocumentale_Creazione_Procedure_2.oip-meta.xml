<?xml version="1.0" encoding="UTF-8"?>
<OmniIntegrationProcedure xmlns="http://soap.sforce.com/2006/04/metadata">
    <customJavaScript>{
    &quot;accountId&quot;: &quot;0019X000015e2ZLQAY&quot;,
    &quot;documentId&quot;: &quot;0699O00000CfaGjQAJ&quot;,
    &quot;externalId&quot;: &quot;SOC_1&quot;,
    &quot;userId&quot;: &quot;0059X00000Jr08cQAB&quot;
}</customJavaScript>
    <description>Integration Procedure utilizzata per creare documenti in Anag, Input: documentId, externalId, accountId</description>
    <elementTypeComponentMapping>{&quot;ElementTypeToHTMLTemplateList&quot;:[]}</elementTypeComponentMapping>
    <isActive>false</isActive>
    <isIntegProcdSignatureAvl>false</isIntegProcdSignatureAvl>
    <isIntegrationProcedure>true</isIntegrationProcedure>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <isMetadataCacheDisabled>false</isMetadataCacheDisabled>
    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
    <isTestProcedure>false</isTestProcedure>
    <isWebCompEnabled>false</isWebCompEnabled>
    <language>Procedure</language>
    <name>UniDocumentale_Creazione</name>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>DeleteDocument</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;documentId&quot; : &quot;%documentId%&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;UniDS_DeleteOmniscriptDocument&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;disableChainable&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;IntegrationProcedureAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>9.0</sequenceNumber>
        <type>Integration Procedure Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Deserialize</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;bodytemp&quot; : &quot;=DESERIALIZE(%body%)&quot;
  },
  &quot;responseJSONPath&quot; : &quot;bodytemp&quot;,
  &quot;responseJSONNode&quot; : &quot;body&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues4&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>5.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ExtractUserInfos</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;UtilGetUserInfo&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;accountDetailsId&quot;,
    &quot;element&quot; : &quot;GetDocInfo:AccountDetailsId&quot;
  }, {
    &quot;inputParam&quot; : &quot;userId&quot;,
    &quot;element&quot; : &quot;userId&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>2.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>GetDocInfo</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;UniGetContentDocumentAndVersionInfo&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;documentId&quot;,
    &quot;element&quot; : &quot;documentId&quot;
  }, {
    &quot;inputParam&quot; : &quot;accountId&quot;,
    &quot;element&quot; : &quot;accountId&quot;
  }, {
    &quot;inputParam&quot; : &quot;externalId&quot;,
    &quot;element&quot; : &quot;externalId&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>1.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>IntegrationService</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;integrationId&quot; : &quot;=\&quot;PostDocumentale_Creazione\&quot;&quot;,
    &quot;body&quot; : &quot;%bodyFinal%&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;ipResponse&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;IntegrationService_IntegrationService&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;disableChainable&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;IntegrationProcedureAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>8.0</sequenceNumber>
        <type>Integration Procedure Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ResponseAction</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;response&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>11.0</sequenceNumber>
        <type>Response Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetDatiTracciatura</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;datiTracciatura&quot; : {
      &quot;applicazioneChiamante&quot; : &quot;hiwamabeigluwavj&quot;,
      &quot;canaleAggiornamento&quot; : &quot;Clayton Ball&quot;,
      &quot;canaleCreazione&quot; : &quot;lekosmevij&quot;,
      &quot;compagniaAggiornamento&quot; : &quot;Samuel Greene&quot;,
      &quot;compagniaCreazione&quot; : &quot;kowzotoujev&quot;,
      &quot;dataCreazione&quot; : &quot;yyyy-MM-dd&apos;T&apos;HH:mm:ss.SSSSSS&quot;,
      &quot;dataUltimoAggiornamento&quot; : &quot;yyyy-MM-dd&apos;T&apos;HH:mm:ss.SSSSSS&quot;,
      &quot;proceduraChiamante&quot; : &quot;runrin&quot;,
      &quot;sistemaAggiornamento&quot; : &quot;Johanna Jones&quot;,
      &quot;sistemaCreazione&quot; : &quot;neeftucludj&quot;,
      &quot;sottosistemaAggiornamento&quot; : &quot;Lewis Myers&quot;,
      &quot;sottosistemaCreazione&quot; : &quot;haglolberiv&quot;,
      &quot;userIdCreazione&quot; : &quot;Christine&quot;,
      &quot;userIdUltimoAggiornamento&quot; : &quot;Millie&quot;,
      &quot;usernameCreazione&quot; : &quot;Eddie&quot;,
      &quot;usernameUltimoAggiornamento&quot; : &quot;Melvin&quot;
    }
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues4&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>6.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <description>file --&gt; %GetDocInfo:VersionData%</description>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetFinalBody</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;username&quot; : &quot;%ExtractUserInfos:username%&quot;,
    &quot;userId&quot; : &quot;%ExtractUserInfos:userId%&quot;,
    &quot;compagnia&quot; : &quot;=IF(%externalId% == \&quot;SOC_1\&quot;, \&quot;unipolsai\&quot;,\&quot;unisalute\&quot;)&quot;,
    &quot;ciu&quot; : &quot;%GetDocInfo:Ciu%&quot;,
    &quot;codiceBelfioreComune&quot; : &quot;%codiceBelfioreComune%&quot;,
    &quot;codiceBelfioreNazione&quot; : &quot;%codiceBelfioreNazione%&quot;,
    &quot;codiceFilenet&quot; : &quot;ABC123&quot;,
    &quot;codiceProvincia&quot; : &quot;%codiceProvincia%&quot;,
    &quot;dataRilascio&quot; : &quot;%dataRilascio%&quot;,
    &quot;dataScadenza&quot; : &quot;%dataScadenza%&quot;,
    &quot;enteRilascio&quot; : &quot;001&quot;,
    &quot;numeroDocumento&quot; : &quot;%numeroDocumento%&quot;,
    &quot;profiled&quot; : false,
    &quot;statoDelDocumento&quot; : &quot;V&quot;,
    &quot;tipoDelDocumento&quot; : &quot;%tipoDelDocumento%&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;bodyFinal&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues6&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>7.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetParamsQuery</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : { },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;params:Params:QueryParam&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues3&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>4.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetParamsUrl</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : { },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;params:Params:PathParam&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>3.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetResponse</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;result&quot; : &quot;=DESERIALIZE(ipResponse:result)&quot;
  },
  &quot;responseJSONPath&quot; : &quot;result&quot;,
  &quot;responseJSONNode&quot; : &quot;response&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>10.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessKey>UniDocumentale_Creazione</omniProcessKey>
    <omniProcessType>Integration Procedure</omniProcessType>
    <propertySetConfig>{
  &quot;linkToExternalObject&quot; : &quot;&quot;,
  &quot;trackingCustomData&quot; : { },
  &quot;includeAllActionsInResponse&quot; : false,
  &quot;columnsPropertyMap&quot; : [ ],
  &quot;relationshipFieldsMap&quot; : [ ],
  &quot;labelSingular&quot; : &quot;&quot;,
  &quot;labelPlural&quot; : &quot;&quot;,
  &quot;description&quot; : &quot;&quot;,
  &quot;nameColumn&quot; : &quot;&quot;,
  &quot;rollbackOnError&quot; : false,
  &quot;chainableQueriesLimit&quot; : 50,
  &quot;chainableDMLStatementsLimit&quot; : null,
  &quot;chainableCpuLimit&quot; : 2000,
  &quot;chainableHeapSizeLimit&quot; : null,
  &quot;chainableDMLRowsLimit&quot; : null,
  &quot;chainableQueryRowsLimit&quot; : null,
  &quot;chainableSoslQueriesLimit&quot; : null,
  &quot;chainableActualTimeLimit&quot; : null,
  &quot;additionalChainableResponse&quot; : { },
  &quot;queueableChainableQueriesLimit&quot; : 120,
  &quot;queueableChainableCpuLimit&quot; : 40000,
  &quot;queueableChainableHeapSizeLimit&quot; : 6,
  &quot;ttlMinutes&quot; : 5,
  &quot;mockResponseMap&quot; : { }
}</propertySetConfig>
    <subType>Creazione</subType>
    <type>UniDocumentale</type>
    <uniqueName>UniDocumentale_Creazione_Procedure_2</uniqueName>
    <versionNumber>2.0</versionNumber>
    <webComponentKey>ed908fa3-d5b7-6243-1dc0-89207fdd8438</webComponentKey>
</OmniIntegrationProcedure>
