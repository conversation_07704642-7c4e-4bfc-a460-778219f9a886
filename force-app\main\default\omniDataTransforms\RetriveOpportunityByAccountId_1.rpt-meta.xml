<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;vlcTimeTracking&quot; : {
    &quot;DataMapperExtractAction1&quot; : null
  },
  &quot;vlcPersistentComponent&quot; : { },
  &quot;userTimeZone&quot; : null,
  &quot;userProfile&quot; : null,
  &quot;userName&quot; : null,
  &quot;userId&quot; : null,
  &quot;timeStamp&quot; : null,
  &quot;ContextId&quot; : null
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>RetriveOpportunityByAccountId</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>Limit</filterValue>
        <globalKey>RetriveOpportunityByAccountIdCustom0jI9O000000uIyLUAUItem0</globalKey>
        <inputObjectName>Opportunity</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveOpportunityByAccountId</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Opportunity</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveOpportunityByAccountIdCustom0jI9O000000uIyLUAUItem12</globalKey>
        <inputFieldName>Opportunity:Name</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveOpportunityByAccountId</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>opp:name</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveOpportunityByAccountIdCustom0jI9O000000uIyLUAUItem11</globalKey>
        <inputFieldName>Opportunity:WorkingSLAExpiryDate__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveOpportunityByAccountId</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>opp:workingSLAExpireDate</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveOpportunityByAccountIdCustom0jI9O000000uIyLUAUItem10</globalKey>
        <inputFieldName>Opportunity:Channel__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveOpportunityByAccountId</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>opp:channel</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveOpportunityByAccountIdCustom0jI9O000000uIyLUAUItem9</globalKey>
        <inputFieldName>Opportunity:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveOpportunityByAccountId</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>opp:opportunityId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveOpportunityByAccountIdCustom0jI9O000000uIyLUAUItem8</globalKey>
        <inputFieldName>Opportunity:AreaOfNeed__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveOpportunityByAccountId</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>opp:areaOfNeed</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveOpportunityByAccountIdCustom0jI9O000000uIyLUAUItem7</globalKey>
        <inputFieldName>Opportunity:AssignedTo__r.Name</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveOpportunityByAccountId</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>opp:AssignedToName</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveOpportunityByAccountIdCustom0jI9O000000uIyLUAUItem6</globalKey>
        <inputFieldName>Opportunity:StageName</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveOpportunityByAccountId</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>opp:status</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>ORDER BY</filterOperator>
        <filterValue>CreatedDate DESC</filterValue>
        <globalKey>RetriveOpportunityByAccountIdCustom0jI9O000000uIyLUAUItem5</globalKey>
        <inputObjectName>Opportunity</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveOpportunityByAccountId</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Opportunity</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountId</filterValue>
        <globalKey>RetriveOpportunityByAccountIdCustom0jI9O000000uIyLUAUItem4</globalKey>
        <inputFieldName>AccountId</inputFieldName>
        <inputObjectName>Opportunity</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveOpportunityByAccountId</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Opportunity</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Opportunity LIST &apos;RecordType.DeveloperName/\/\/==/\/\/&quot;Omnicanale&quot;/\/\/OR/\/\/RecordType.DeveloperName/\/\/==/\/\/&quot;Agenziale&quot;&apos; FILTER</formulaConverted>
        <formulaExpression>FILTER(LIST(Opportunity), &apos;RecordType.DeveloperName == &quot;Omnicanale&quot; OR RecordType.DeveloperName == &quot;Agenziale&quot;&apos;)</formulaExpression>
        <formulaResultPath>filterListOpp_temp</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>RetriveOpportunityByAccountIdCustom0jI9O000000uIyLUAUItem3</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveOpportunityByAccountId</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:filterListOpp_temp ISBLANK 0 | var:filterListOpp_temp LISTSIZE IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(filterListOpp_temp),0,LISTSIZE(filterListOpp_temp))</formulaExpression>
        <formulaResultPath>omniCount</formulaResultPath>
        <formulaSequence>2.0</formulaSequence>
        <globalKey>RetriveOpportunityByAccountIdCustom0jI9O000000uIyLUAUItem2</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveOpportunityByAccountId</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveOpportunityByAccountIdCustom0jI9O000000uIyLUAUItem14</globalKey>
        <inputFieldName>Opportunity:RecordType.DeveloperName</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveOpportunityByAccountId</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>opp:oppType</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>&lt;&gt;</filterOperator>
        <filterValue>&apos;Chiuso&apos;</filterValue>
        <globalKey>RetriveOpportunityByAccountIdCustom0jI9O000000uIyLUAUItem1</globalKey>
        <inputFieldName>StageName</inputFieldName>
        <inputObjectName>Opportunity</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveOpportunityByAccountId</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Opportunity</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveOpportunityByAccountIdCustom0jI9O000000uIyLUAUItem13</globalKey>
        <inputFieldName>omniCount</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveOpportunityByAccountId</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>omniCount</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;AccountId&quot; : &quot;0019X00001CrPH2QAN&quot;,
  &quot;Limit&quot; : &quot;10&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>RetriveOpportunityByAccountId_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
