------------------------------------------------------------------------------
--------------------------------- DESCRIPTION ---------------------------------
-------------------------------------------------------------------------------
 
This manual procedure aims at populating the decision matrices used in various
automated processes with their respective data.
 
-------------------------------------------------------------------------------
--------------------------- MANUAL PROCEDURE STEPS ----------------------------
-------------------------------------------------------------------------------
 
1. Login to Salesforce
2. Open the App Launcher
3. In the search bar, type "Lookup Tables"
4. Click on "Lookup Tables"
5. Choose All Decision Matrix, EnteEmissioneDocumento
6. Extract the contents of the file "EnteEmissioneDocumento V1_2025-07-24 17_28_43.zip"
7. Refer to the file "EnteEmissioneDocumento V1_2025-07-24 17_28_43.csv" extracted to infer
   the relationships between the .csv files and their respective tables

Column mapping:
1. flagAttivo
2. descrizione
3. codice

Steps to follow for each row:
1. Select the record whose label corresponds to the content of the Matrix Label column
2. Select the matrix version corresponding to the content of the Matrix Version column
3. Click the "Details" tab
4. If the "Active" box is checked, click the edit button on the "Active" field, uncheck it and save
5. Click the "Matrix" tab
6. Click the "Upload CSV File" button
7. Click the "Upload Files" button
8. In the File Explorer, select the file whose name corresponds to the content of the
   Data File column and select Overwrite
9. Check the uploaded data. If any cell containing special characters looks corrupted,
   edit the cell's content and save
10. Once finished checking, click the "Details" tab, check the "Active" box and save
 No newline at end of file
