<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;Custom&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;body&quot;:&quot;{\n  \&quot;period\&quot;: \&quot;today\&quot;,\n  \&quot;fromDate\&quot;: \&quot;\&quot;,\n  \&quot;toDate\&quot;: \&quot;\&quot;,\n  \&quot;referent\&quot;: \&quot;\&quot;,\n  \&quot;section\&quot;: \&quot;\&quot;,\n  \&quot;rate\&quot;: false,\n  \&quot;periods\&quot;: [\n    {\n      \&quot;label\&quot;: \&quot;Oggi\&quot;,\n      \&quot;value\&quot;: \&quot;today\&quot;\n    },\n    {\n      \&quot;label\&quot;: \&quot;Settimana corrente\&quot;,\n      \&quot;value\&quot;: \&quot;week\&quot;\n    },\n    {\n      \&quot;label\&quot;: \&quot;Mese corrente\&quot;,\n      \&quot;value\&quot;: \&quot;month\&quot;\n    },\n    {\n      \&quot;label\&quot;: \&quot;Da - A\&quot;,\n      \&quot;value\&quot;: \&quot;fromTo\&quot;\n    }\n  ],\n  \&quot;referentes\&quot;: [\n    {\n      \&quot;label\&quot;: \&quot;Mario Rossi\&quot;,\n      \&quot;value\&quot;: \&quot;SDADSDASDADAAD\&quot;\n    },\n    {\n      \&quot;label\&quot;: \&quot;Mario Verdi\&quot;,\n      \&quot;value\&quot;: \&quot;ADSADASDSADAD\&quot;\n    }\n  ],\n  \&quot;sections\&quot;: [\n    {\n      \&quot;label\&quot;: \&quot;Tutti\&quot;,\n      \&quot;value\&quot;: \&quot;RCA;REL;VIT\&quot;\n    },\n    {\n      \&quot;label\&quot;: \&quot;Auto\&quot;,\n      \&quot;value\&quot;: \&quot;RCA\&quot;\n    },\n    {\n      \&quot;label\&quot;: \&quot;Danni Non Auto\&quot;,\n      \&quot;value\&quot;: \&quot;REL\&quot;\n    },\n    {\n      \&quot;label\&quot;: \&quot;Vita\&quot;,\n      \&quot;value\&quot;: \&quot;VIT\&quot;\n    }\n  ],\n  \&quot;rates\&quot;: [\n    {\n      \&quot;label\&quot;: \&quot;Si\&quot;,\n      \&quot;value\&quot;: true\n    },\n    {\n      \&quot;label\&quot;: \&quot;No\&quot;,\n      \&quot;value\&quot;: false\n    }\n  ]\n}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[]}}</dataSourceConfig>
    <isActive>true</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>HPRenewalDashboard</name>
    <omniUiCardType>Parent</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Periodo&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;fieldBinding&quot;:&quot;{period}&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1753318233968-slcw0uect&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753359998154&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;PubSub&quot;,&quot;eventName&quot;:&quot;HPRenewalDashboardChild&quot;,&quot;message&quot;:&quot;reload&quot;,&quot;hasExtraParams&quot;:false},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1753436121259-kgyhyctd1&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753440722422&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[]},&quot;actionIndex&quot;:1}],&quot;showSpinner&quot;:&quot;false&quot;},&quot;value&quot;:&quot;&quot;,&quot;options&quot;:[],&quot;customProperties&quot;:[{&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{periods}&quot;,&quot;id&quot;:0}]},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1753318233968-slcw0uect&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753359998154&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;PubSub&quot;,&quot;eventName&quot;:&quot;HPRenewalDashboardChild&quot;,&quot;message&quot;:&quot;reload&quot;,&quot;hasExtraParams&quot;:false},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1753436121259-kgyhyctd1&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753440722422&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[]},&quot;actionIndex&quot;:1}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;}},&quot;elementLabel&quot;:&quot;Select-0&quot;},{&quot;name&quot;:&quot;Date&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;date&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Da&quot;,&quot;format&quot;:&quot;DD-MM-YYYY&quot;,&quot;outputFormat&quot;:&quot;DD-MM-YYYY&quot;,&quot;name&quot;:&quot;Date-1&quot;,&quot;customProperties&quot;:[],&quot;fieldBinding&quot;:&quot;{fromDate}&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1753440766655-4phzlftrd&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753440766721&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;PubSub&quot;,&quot;eventName&quot;:&quot;HPRenewalDashboardChild&quot;,&quot;message&quot;:&quot;reolad&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;},&quot;max&quot;:&quot;&quot;},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;period&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;fromTo&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1753440766655-4phzlftrd&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753440766721&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;PubSub&quot;,&quot;eventName&quot;:&quot;HPRenewalDashboardChild&quot;,&quot;message&quot;:&quot;reolad&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;}},&quot;elementLabel&quot;:&quot;Date-1&quot;},{&quot;name&quot;:&quot;Date&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;date&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;A&quot;,&quot;format&quot;:&quot;DD-MM-YYYY&quot;,&quot;outputFormat&quot;:&quot;DD-MM-YYYY&quot;,&quot;name&quot;:&quot;Date-1&quot;,&quot;customProperties&quot;:[],&quot;fieldBinding&quot;:&quot;{toDate}&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1753440810651-yi4pfptx4&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753440810716&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;PubSub&quot;,&quot;eventName&quot;:&quot;HPRenewalDashboardChild&quot;,&quot;message&quot;:&quot;reload&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;period&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;fromTo&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1753440810651-yi4pfptx4&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753440810716&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;PubSub&quot;,&quot;eventName&quot;:&quot;HPRenewalDashboardChild&quot;,&quot;message&quot;:&quot;reload&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;}},&quot;elementLabel&quot;:&quot;Date-2&quot;},{&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Comparto&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;fieldBinding&quot;:&quot;{section}&quot;,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{sections}&quot;,&quot;id&quot;:0}],&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1753440842285-tw93swr18&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753440842349&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;PubSub&quot;,&quot;eventName&quot;:&quot;HPRenewalDashboardChild&quot;,&quot;message&quot;:&quot;reolad&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;},&quot;value&quot;:&quot;&quot;,&quot;placeholder&quot;:&quot;&quot;,&quot;options&quot;:[]},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1753440842285-tw93swr18&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753440842349&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;PubSub&quot;,&quot;eventName&quot;:&quot;HPRenewalDashboardChild&quot;,&quot;message&quot;:&quot;reolad&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;},&quot;elementLabel&quot;:&quot;Select-3&quot;},{&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Mensilizzate&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;fieldBinding&quot;:&quot;{rate}&quot;,&quot;options&quot;:[],&quot;customProperties&quot;:[{&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{rates}&quot;,&quot;id&quot;:0}],&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1753440858409-n66bmsoqu&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753440858475&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;PubSub&quot;,&quot;eventName&quot;:&quot;HPRenewalDashboardChild&quot;,&quot;message&quot;:&quot;reload&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;},&quot;value&quot;:&quot;No&quot;},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1753440858409-n66bmsoqu&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753440858475&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;PubSub&quot;,&quot;eventName&quot;:&quot;HPRenewalDashboardChild&quot;,&quot;message&quot;:&quot;reload&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;}},&quot;elementLabel&quot;:&quot;Select-4&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[],&quot;elementLabel&quot;:&quot;Block-5&quot;},{&quot;name&quot;:&quot;FlexCard&quot;,&quot;element&quot;:&quot;childCardPreview&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;cardName&quot;:&quot;HPRenewalDashboardChild&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;cardNode&quot;:&quot;&quot;,&quot;parentAttribute&quot;:{&quot;period&quot;:&quot;{period}&quot;,&quot;fromDate&quot;:&quot;{fromDate}&quot;,&quot;toDate&quot;:&quot;{toDate}&quot;,&quot;section&quot;:&quot;{section}&quot;,&quot;rate&quot;:&quot;{rate}&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;HPRenewalDashboardChild&quot;:&quot;FlexCard-6&quot;,&quot;elementLabel&quot;:&quot;FlexElementChild&quot;,&quot;userUpdatedElementLabel&quot;:true}]}},&quot;childCards&quot;:[&quot;HPRenewalDashboardChild&quot;],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;Custom&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;body&quot;:&quot;{\n  \&quot;period\&quot;: \&quot;today\&quot;,\n  \&quot;fromDate\&quot;: \&quot;\&quot;,\n  \&quot;toDate\&quot;: \&quot;\&quot;,\n  \&quot;referent\&quot;: \&quot;\&quot;,\n  \&quot;section\&quot;: \&quot;\&quot;,\n  \&quot;rate\&quot;: false,\n  \&quot;periods\&quot;: [\n    {\n      \&quot;label\&quot;: \&quot;Oggi\&quot;,\n      \&quot;value\&quot;: \&quot;today\&quot;\n    },\n    {\n      \&quot;label\&quot;: \&quot;Settimana corrente\&quot;,\n      \&quot;value\&quot;: \&quot;week\&quot;\n    },\n    {\n      \&quot;label\&quot;: \&quot;Mese corrente\&quot;,\n      \&quot;value\&quot;: \&quot;month\&quot;\n    },\n    {\n      \&quot;label\&quot;: \&quot;Da - A\&quot;,\n      \&quot;value\&quot;: \&quot;fromTo\&quot;\n    }\n  ],\n  \&quot;referentes\&quot;: [\n    {\n      \&quot;label\&quot;: \&quot;Mario Rossi\&quot;,\n      \&quot;value\&quot;: \&quot;SDADSDASDADAAD\&quot;\n    },\n    {\n      \&quot;label\&quot;: \&quot;Mario Verdi\&quot;,\n      \&quot;value\&quot;: \&quot;ADSADASDSADAD\&quot;\n    }\n  ],\n  \&quot;sections\&quot;: [\n    {\n      \&quot;label\&quot;: \&quot;Tutti\&quot;,\n      \&quot;value\&quot;: \&quot;RCA;REL;VIT\&quot;\n    },\n    {\n      \&quot;label\&quot;: \&quot;Auto\&quot;,\n      \&quot;value\&quot;: \&quot;RCA\&quot;\n    },\n    {\n      \&quot;label\&quot;: \&quot;Danni Non Auto\&quot;,\n      \&quot;value\&quot;: \&quot;REL\&quot;\n    },\n    {\n      \&quot;label\&quot;: \&quot;Vita\&quot;,\n      \&quot;value\&quot;: \&quot;VIT\&quot;\n    }\n  ],\n  \&quot;rates\&quot;: [\n    {\n      \&quot;label\&quot;: \&quot;Si\&quot;,\n      \&quot;value\&quot;: true\n    },\n    {\n      \&quot;label\&quot;: \&quot;No\&quot;,\n      \&quot;value\&quot;: false\n    }\n  ]\n}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[]},&quot;title&quot;:&quot;HPRenewalDashboard&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;xmlObject&quot;:{&quot;targetConfigs&quot;:&quot;PHRhcmdldENvbmZpZyB0YXJnZXRzPSJsaWdodG5pbmdfX0FwcFBhZ2UiPgogICAgICAgICAgICAgICAgICAgIDxwcm9wZXJ0eSBuYW1lPSJkZWJ1ZyIgdHlwZT0iQm9vbGVhbiIvPgogICAgICAgICAgICAgICAgICAgIDxwcm9wZXJ0eSBuYW1lPSJyZWNvcmRJZCIgdHlwZT0iU3RyaW5nIi8+CiAgICAgICAgICAgICAgICA8L3RhcmdldENvbmZpZz4KICAgICAgICAgICAgICAgIDx0YXJnZXRDb25maWcgdGFyZ2V0cz0ibGlnaHRuaW5nX19SZWNvcmRQYWdlIj4KICAgICAgICAgICAgICAgICAgICA8cHJvcGVydHkgbmFtZT0iZGVidWciIHR5cGU9IkJvb2xlYW4iLz4KICAgICAgICAgICAgICAgIDwvdGFyZ2V0Q29uZmlnPg==&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__RecordPage&quot;,&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;]},&quot;apiVersion&quot;:56,&quot;masterLabel&quot;:&quot;HPRenewalDashboard&quot;,&quot;description&quot;:&quot;&quot;,&quot;runtimeNamespace&quot;:&quot;&quot;,&quot;isExposed&quot;:true},&quot;xmlJson&quot;:[{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__AppPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]},{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__RecordPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}}]}],&quot;multilanguageSupport&quot;:false,&quot;listenToWidthResize&quot;:false,&quot;isRepeatable&quot;:true,&quot;hideChildCardPreview&quot;:false}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;period&quot;:&quot;today&quot;,&quot;fromDate&quot;:&quot;&quot;,&quot;toDate&quot;:&quot;&quot;,&quot;referent&quot;:&quot;&quot;,&quot;section&quot;:&quot;&quot;,&quot;rate&quot;:false,&quot;periods&quot;:[{&quot;label&quot;:&quot;Oggi&quot;,&quot;value&quot;:&quot;today&quot;},{&quot;label&quot;:&quot;Settimana corrente&quot;,&quot;value&quot;:&quot;week&quot;},{&quot;label&quot;:&quot;Mese corrente&quot;,&quot;value&quot;:&quot;month&quot;},{&quot;label&quot;:&quot;Da - A&quot;,&quot;value&quot;:&quot;fromTo&quot;}],&quot;referentes&quot;:[{&quot;label&quot;:&quot;Mario Rossi&quot;,&quot;value&quot;:&quot;SDADSDASDADAAD&quot;},{&quot;label&quot;:&quot;Mario Verdi&quot;,&quot;value&quot;:&quot;ADSADASDSADAD&quot;}],&quot;sections&quot;:[{&quot;label&quot;:&quot;Tutti&quot;,&quot;value&quot;:&quot;RCA;REL;VIT&quot;},{&quot;label&quot;:&quot;Auto&quot;,&quot;value&quot;:&quot;RCA&quot;},{&quot;label&quot;:&quot;Danni Non Auto&quot;,&quot;value&quot;:&quot;REL&quot;},{&quot;label&quot;:&quot;Vita&quot;,&quot;value&quot;:&quot;VIT&quot;}],&quot;rates&quot;:[{&quot;label&quot;:&quot;Si&quot;,&quot;value&quot;:true},{&quot;label&quot;:&quot;No&quot;,&quot;value&quot;:false}]}</sampleDataSourceResponse>
    <versionNumber>2</versionNumber>
</OmniUiCard>
