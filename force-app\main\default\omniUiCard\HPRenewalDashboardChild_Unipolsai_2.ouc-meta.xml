<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;RestPost_Renewals&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;period&quot;:&quot;{Parent.period}&quot;,&quot;fromDate&quot;:&quot;{Parent.fromDate}&quot;,&quot;toDate&quot;:&quot;{Parent.toDate}&quot;,&quot;section&quot;:&quot;{Parent.section}&quot;,&quot;rate&quot;:&quot;{Parent.rate}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;Parent.period\&quot;:\&quot;{Parent.period}\&quot;,\&quot;Parent.fromDate\&quot;:\&quot;{Parent.fromDate}\&quot;,\&quot;Parent.toDate\&quot;:\&quot;{Parent.toDate}\&quot;,\&quot;Parent.section\&quot;:\&quot;{Parent.section}\&quot;,\&quot;Parent.rate\&quot;:\&quot;{Parent.rate}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;Parent.period&quot;,&quot;val&quot;:&quot;fromTo&quot;,&quot;id&quot;:21},{&quot;name&quot;:&quot;Parent.fromDate&quot;,&quot;val&quot;:&quot;2025-01-01&quot;,&quot;id&quot;:22},{&quot;name&quot;:&quot;Parent.toDate&quot;,&quot;val&quot;:&quot;2025-02-01&quot;,&quot;id&quot;:23},{&quot;name&quot;:&quot;Parent.section&quot;,&quot;val&quot;:&quot;RCA;VIT&quot;,&quot;id&quot;:24},{&quot;name&quot;:&quot;Parent.rate&quot;,&quot;val&quot;:&quot;true&quot;,&quot;id&quot;:25}]}}</dataSourceConfig>
    <isActive>true</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>HPRenewalDashboardChild</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;none&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Chart&quot;,&quot;element&quot;:&quot;flexChart&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;type&quot;:&quot;doughnut&quot;,&quot;hideHeader&quot;:&quot;true&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;records&quot;:&quot;{records}&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;title&quot;:&quot;Chart&quot;,&quot;channel&quot;:&quot;flexcarddesigner:chart&quot;,&quot;cutoutPercentage&quot;:0,&quot;colorPalette&quot;:[&quot;#52B7D8&quot;,&quot;#E16032&quot;,&quot;#FFB03B&quot;,&quot;#54A77B&quot;,&quot;#4FD2D2&quot;,&quot;#E287B2&quot;],&quot;labelNode&quot;:&quot;label&quot;,&quot;valueNode&quot;:&quot;premio&quot;,&quot;maintainAspectRatio&quot;:false,&quot;aspectRatio&quot;:&quot;1&quot;,&quot;chartHeight&quot;:&quot;&quot;,&quot;displayLegend&quot;:&quot;true&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Chart-0&quot;}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;RestPost_Renewals&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;period&quot;:&quot;{Parent.period}&quot;,&quot;fromDate&quot;:&quot;{Parent.fromDate}&quot;,&quot;toDate&quot;:&quot;{Parent.toDate}&quot;,&quot;section&quot;:&quot;{Parent.section}&quot;,&quot;rate&quot;:&quot;{Parent.rate}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;Parent.period\&quot;:\&quot;{Parent.period}\&quot;,\&quot;Parent.fromDate\&quot;:\&quot;{Parent.fromDate}\&quot;,\&quot;Parent.toDate\&quot;:\&quot;{Parent.toDate}\&quot;,\&quot;Parent.section\&quot;:\&quot;{Parent.section}\&quot;,\&quot;Parent.rate\&quot;:\&quot;{Parent.rate}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;Parent.period&quot;,&quot;val&quot;:&quot;fromTo&quot;,&quot;id&quot;:21},{&quot;name&quot;:&quot;Parent.fromDate&quot;,&quot;val&quot;:&quot;2025-01-01&quot;,&quot;id&quot;:22},{&quot;name&quot;:&quot;Parent.toDate&quot;,&quot;val&quot;:&quot;2025-02-01&quot;,&quot;id&quot;:23},{&quot;name&quot;:&quot;Parent.section&quot;,&quot;val&quot;:&quot;RCA;VIT&quot;,&quot;id&quot;:24},{&quot;name&quot;:&quot;Parent.rate&quot;,&quot;val&quot;:&quot;true&quot;,&quot;id&quot;:25}]},&quot;title&quot;:&quot;HPRenewalDashboardChild&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;isRepeatable&quot;:false,&quot;xmlObject&quot;:{&quot;isExplicitImport&quot;:false,&quot;targetConfigs&quot;:&quot;&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;,&quot;lightning__RecordPage&quot;]}},&quot;sessionVars&quot;:[],&quot;xmlJson&quot;:[],&quot;events&quot;:[{&quot;eventname&quot;:&quot;reload&quot;,&quot;channelname&quot;:&quot;HPRenewalDashboardChild&quot;,&quot;element&quot;:&quot;action&quot;,&quot;eventtype&quot;:&quot;pubsub&quot;,&quot;recordIndex&quot;:&quot;0&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1753319037247-apy813qap&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753319037271&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;reload&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;key&quot;:&quot;event-0&quot;,&quot;displayLabel&quot;:&quot;HPRenewalDashboardChild:reload&quot;,&quot;eventLabel&quot;:&quot;pubsub&quot;}],&quot;hideChildCardPreview&quot;:false,&quot;osSupport&quot;:true}</propertySetConfig>
    <sampleDataSourceResponse>[{&quot;label&quot;:&quot;Incassati&quot;,&quot;numero&quot;:0,&quot;premio&quot;:0},{&quot;label&quot;:&quot;Non Incassati&quot;,&quot;numero&quot;:0,&quot;premio&quot;:0},{&quot;label&quot;:&quot;Totale&quot;,&quot;numero&quot;:0,&quot;premio&quot;:0}]</sampleDataSourceResponse>
    <versionNumber>2</versionNumber>
</OmniUiCard>
