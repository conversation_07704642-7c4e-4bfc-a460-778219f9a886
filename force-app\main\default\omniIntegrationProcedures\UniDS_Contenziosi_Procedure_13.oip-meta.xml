<?xml version="1.0" encoding="UTF-8"?>
<OmniIntegrationProcedure xmlns="http://soap.sforce.com/2006/04/metadata">
    <customJavaScript>{
    &quot;AccountId&quot;: &quot;0019O00000spdgXQAQ&quot;,
    &quot;UserId&quot;: &quot;0059O00000WEYgvQAH&quot;,
    &quot;isAbbinato&quot;: &quot;false&quot;
}</customJavaScript>
    <description>New version with managed response in case of contatore = 0</description>
    <elementTypeComponentMapping>{&quot;ElementTypeToHTMLTemplateList&quot;:[]}</elementTypeComponentMapping>
    <isActive>true</isActive>
    <isIntegProcdSignatureAvl>false</isIntegProcdSignatureAvl>
    <isIntegrationProcedure>true</isIntegrationProcedure>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <isMetadataCacheDisabled>false</isMetadataCacheDisabled>
    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
    <isTestProcedure>false</isTestProcedure>
    <isWebCompEnabled>false</isWebCompEnabled>
    <language>Procedure</language>
    <name>UniDS_Contenziosi</name>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>CheckCustomPermissions</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;additionalInput&quot; : {
    &quot;customPermissions&quot; : &quot;Array (click &apos;Edit as JSON&apos; to modify)&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;label&quot; : &quot;RemoteAction1&quot;,
  &quot;remoteClass&quot; : &quot;OmnistudioUtils&quot;,
  &quot;remoteMethod&quot; : &quot;checkCustomPermissions&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;responseJSONNode&quot; : &quot;checkCp&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;useFormulas&quot; : true
}</propertySetConfig>
        <sequenceNumber>1.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>CheckIsAbbinato</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;additionalInput&quot; : {
    &quot;accountId&quot; : &quot;%AccountId%&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;label&quot; : &quot;RemoteAction2&quot;,
  &quot;remoteClass&quot; : &quot;OmnistudioUtils&quot;,
  &quot;remoteMethod&quot; : &quot;checkAbbinato&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;responseJSONNode&quot; : &quot;isAbbinato&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;useFormulas&quot; : true
}</propertySetConfig>
        <sequenceNumber>2.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>getAccountTitoliInScadenza</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;bundle&quot; : &quot;getAccountTitoliInScadenza&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;element&quot; : &quot;AccountId&quot;,
    &quot;inputParam&quot; : &quot;idToSearch&quot;
  }, {
    &quot;element&quot; : &quot;UserId&quot;,
    &quot;inputParam&quot; : &quot;UserId&quot;
  } ],
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;ignoreCache&quot; : false,
  &quot;label&quot; : &quot;DataMapperExtractAction2&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;useFormulas&quot; : true
}</propertySetConfig>
        <sequenceNumber>3.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>getDataUnipol</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(%getDataUnipolSetParams:params%) &amp;&amp; ISNOTBLANK(%getDataUnipolSetParams:netUserUnipol%)&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;respUnipol&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/titoli&quot;,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;retryCount&quot; : 0,
  &quot;restOptions&quot; : {
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;headers&quot; : {
      &quot;userId&quot; : &quot;%getDataUnipolSetParams:netUserUnipol%&quot;
    },
    &quot;isCompressed&quot; : false,
    &quot;params&quot; : {
      &quot;agencyCode&quot; : &quot;%getDataUnipolSetParams:params:codiceAgenzia%&quot;,
      &quot;companyCode&quot; : &quot;%getDataUnipolSetParams:params:societa%&quot;,
      &quot;effectiveEndDate&quot; : &quot;%getDataUnipolSetParams:endDate%&quot;,
      &quot;effectiveStartDate&quot; : &quot;%getDataUnipolSetParams:startDate%&quot;,
      &quot;fiscalCode&quot; : &quot;%getDataUnipolSetParams:params:codiceFiscale%&quot;,
      &quot;securityType&quot; : 1,
      &quot;tipoMovimento&quot; : &quot;LEGALE&quot;,
      &quot;userId&quot; : &quot;%getDataUnipolSetParams:netUserUnipol%&quot;
    },
    &quot;sendBody&quot; : false,
    &quot;timeout&quot; : 20000,
    &quot;xmlEscapeResponse&quot; : false
  },
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;label&quot; : &quot;HTTPAction1&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
        <sequenceNumber>9.0</sequenceNumber>
        <type>Rest Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>getDataUnipolSetParams</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;elementValueMap&quot; : {
    &quot;endDate&quot; : &quot;=FORMATDATETIME(ADDDAY((TODAY), 60),\&quot;yyyy-MM-dd\&quot;)&quot;,
    &quot;netUserUnipol&quot; : &quot;%NetworkUserExtractUnipol:NetUser%&quot;,
    &quot;params&quot; : &quot;=FILTER(LIST(%MergeListAccount%), &apos;societa=\&quot;1\&quot;&apos;)&quot;,
    &quot;startDate&quot; : &quot;1900-01-01&quot;
  },
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;label&quot; : &quot;SetValues3&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
        <sequenceNumber>7.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>getDataUniSalute</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(%getDataUniSaluteSetParams:params%) &amp;&amp; ISNOTBLANK(%getDataUniSaluteSetParams:netUserUnisalute%)&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;respUnisalute&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/titoli&quot;,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;retryCount&quot; : 0,
  &quot;restOptions&quot; : {
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;headers&quot; : {
      &quot;userId&quot; : &quot;%getDataUniSaluteSetParams:netUserUnisalute%&quot;
    },
    &quot;isCompressed&quot; : false,
    &quot;params&quot; : {
      &quot;agencyCode&quot; : &quot;%getDataUniSaluteSetParams:params:codiceAgenzia%&quot;,
      &quot;companyCode&quot; : &quot;%getDataUniSaluteSetParams:params:societa%&quot;,
      &quot;effectiveEndDate&quot; : &quot;%getDataUniSaluteSetParams:endDate%&quot;,
      &quot;effectiveStartDate&quot; : &quot;%getDataUniSaluteSetParams:startDate%&quot;,
      &quot;fiscalCode&quot; : &quot;%getDataUniSaluteSetParams:params:codiceFiscale%&quot;,
      &quot;securityType&quot; : 1,
      &quot;tipoMovimento&quot; : &quot;LEGALE&quot;,
      &quot;userId&quot; : &quot;%getDataUniSaluteSetParams:netUserUnisalute%&quot;
    },
    &quot;sendBody&quot; : false,
    &quot;timeout&quot; : 20000,
    &quot;xmlEscapeResponse&quot; : false
  },
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;label&quot; : &quot;HTTPAction1&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
        <sequenceNumber>10.0</sequenceNumber>
        <type>Rest Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>getDataUniSaluteSetParams</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;elementValueMap&quot; : {
    &quot;endDate&quot; : &quot;=FORMATDATETIME(ADDDAY((TODAY), 60),\&quot;yyyy-MM-dd\&quot;)&quot;,
    &quot;netUserUnisalute&quot; : &quot;%NetworkUserExtractUnisalute:NetUser%&quot;,
    &quot;params&quot; : &quot;=FILTER(LIST(%MergeListAccount%), &apos;societa=\&quot;4\&quot;&apos;)&quot;,
    &quot;startDate&quot; : &quot;1900-01-01&quot;
  },
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;label&quot; : &quot;SetValues4&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
        <sequenceNumber>8.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>LogUnipolHTTPError</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;additionalInput&quot; : {
    &quot;ClassName&quot; : &quot;UniDS_Contenziosi&quot;,
    &quot;Message&quot; : &quot;Error HTTP Action&quot;,
    &quot;MethodName&quot; : &quot;Uni_getDataUnipol&quot;,
    &quot;Payload&quot; : &quot;%respUnipol%&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(%respUnipol:error%)&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;label&quot; : &quot;RemoteAction3&quot;,
  &quot;remoteClass&quot; : &quot;UniLogIntegrationProcedureSubmit&quot;,
  &quot;remoteMethod&quot; : &quot;call&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;show&quot; : null,
  &quot;useFormulas&quot; : true
}</propertySetConfig>
        <sequenceNumber>11.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>LogUnisaluteHTTPError</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;additionalInput&quot; : {
    &quot;ClassName&quot; : &quot;UniDS_Contenziosi&quot;,
    &quot;Message&quot; : &quot;Error HTTP&quot;,
    &quot;MethodName&quot; : &quot;Uni_getDataUnisalute&quot;,
    &quot;Payload&quot; : &quot;%respUnisalute%&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;(ISNOTBLANK(%respUnisalute:error%)&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;label&quot; : &quot;RemoteAction3&quot;,
  &quot;remoteClass&quot; : &quot;UniLogIntegrationProcedureSubmit&quot;,
  &quot;remoteMethod&quot; : &quot;call&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;show&quot; : null,
  &quot;useFormulas&quot; : true
}</propertySetConfig>
        <sequenceNumber>12.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>MergeListAccount</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;advancedMerge&quot; : false,
  &quot;advancedMergeMap&quot; : [ ],
  &quot;allowMergeNulls&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;dynamicOutputFields&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;filterListFormula&quot; : &quot;&quot;,
  &quot;hasPrimary&quot; : false,
  &quot;label&quot; : &quot;ListAction2&quot;,
  &quot;mergeFields&quot; : [ &quot;Account:nomeSocieta&quot;, &quot;Account:codiceFiscale&quot;, &quot;Account:societa&quot;, &quot;Account:codiceAgenzia&quot; ],
  &quot;mergeListsOrder&quot; : [ &quot;getAccountTitoliInScadenza:Account&quot; ],
  &quot;preventIntraListMerge&quot; : false,
  &quot;primaryListKey&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;sortBy&quot; : [ ],
  &quot;sortInDescendingOrder&quot; : false,
  &quot;updateFieldValue&quot; : { },
  &quot;useFormulas&quot; : true
}</propertySetConfig>
        <sequenceNumber>4.0</sequenceNumber>
        <type>List Merge Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>MockResponseUnipol</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;elementValueMap&quot; : {
    &quot;responseUnipol&quot; : &quot;=DESERIALIZE(&apos;[{\&quot;uplCrmQuietId\&quot;:\&quot;\&quot;,\&quot;idContrattoPTF\&quot;:\&quot;f5d270eb-0feb-4083-b366-60b7aaa726cd\&quot;,\&quot;xquietanzaId\&quot;:\&quot;213631529\&quot;,\&quot;contId\&quot;:\&quot;922339426762273302\&quot;,\&quot;isFEAAttiva\&quot;:false,\&quot;isFEAVisualizzata\&quot;:true,\&quot;isProdottoUnico\&quot;:true,\&quot;isFirmaAttiva\&quot;:true,\&quot;isRettificaAttiva\&quot;:true,\&quot;isIncassaAttiva\&quot;:true,\&quot;isCompletaAttiva\&quot;:false,\&quot;Unibox\&quot;:\&quot;\&quot;,\&quot;Firmatario\&quot;:\&quot;\&quot;,\&quot;Rettificabile\&quot;:\&quot;\&quot;,\&quot;Titoli al legale\&quot;:\&quot;\&quot;,\&quot;Modello\&quot;:\&quot;\&quot;,\&quot;Targa\&quot;:\&quot;\&quot;,\&quot;Esposizione AR\&quot;:false,\&quot;Omnicanalità\&quot;:\&quot;\&quot;,\&quot;Frazionamento\&quot;:\&quot;MENSILE\&quot;,\&quot;N. Folder\&quot;:\&quot;101853000292438\&quot;,\&quot;Premio\&quot;:0,\&quot;Scadenza\&quot;:\&quot;2025-06-23T22:00:00\&quot;,\&quot;Numero Polizza/Posizione\&quot;:[194024365],\&quot;Ambito\&quot;:\&quot;\&quot;,\&quot;Tipo\&quot;:\&quot;4\&quot;,\&quot;Società\&quot;:\&quot;1\&quot;},{\&quot;uplCrmQuietId\&quot;:\&quot;\&quot;,\&quot;idContrattoPTF\&quot;:\&quot;c783733c-0eba-4cdc-93ff-ef5338436e1d\&quot;,\&quot;xquietanzaId\&quot;:\&quot;213631525\&quot;,\&quot;contId\&quot;:\&quot;922339426762273302\&quot;,\&quot;isFEAAttiva\&quot;:false,\&quot;isFEAVisualizzata\&quot;:true,\&quot;isProdottoUnico\&quot;:true,\&quot;isFirmaAttiva\&quot;:true,\&quot;isRettificaAttiva\&quot;:true,\&quot;isIncassaAttiva\&quot;:true,\&quot;isCompletaAttiva\&quot;:false,\&quot;Unibox\&quot;:\&quot;\&quot;,\&quot;Firmatario\&quot;:\&quot;\&quot;,\&quot;Rettificabile\&quot;:\&quot;\&quot;,\&quot;Titoli al legale\&quot;:\&quot;\&quot;,\&quot;Modello\&quot;:\&quot;\&quot;,\&quot;Targa\&quot;:\&quot;\&quot;,\&quot;Esposizione AR\&quot;:false,\&quot;Omnicanalità\&quot;:\&quot;\&quot;,\&quot;Frazionamento\&quot;:\&quot;MENSILE\&quot;,\&quot;N. Folder\&quot;:\&quot;101853000292426\&quot;,\&quot;Premio\&quot;:0,\&quot;Scadenza\&quot;:\&quot;2025-06-23T22:00:00\&quot;,\&quot;Numero Polizza/Posizione\&quot;:[192165386],\&quot;Ambito\&quot;:\&quot;\&quot;,\&quot;Tipo\&quot;:\&quot;4\&quot;,\&quot;Società\&quot;:\&quot;1\&quot;},{\&quot;uplCrmQuietId\&quot;:\&quot;\&quot;,\&quot;idContrattoPTF\&quot;:\&quot;fc6fc21a-9ba6-413c-894c-fb00b04ea34a\&quot;,\&quot;xquietanzaId\&quot;:\&quot;213622222\&quot;,\&quot;contId\&quot;:\&quot;922339426762273302\&quot;,\&quot;isFEAAttiva\&quot;:false,\&quot;isFEAVisualizzata\&quot;:true,\&quot;isProdottoUnico\&quot;:true,\&quot;isFirmaAttiva\&quot;:true,\&quot;isRettificaAttiva\&quot;:true,\&quot;isIncassaAttiva\&quot;:true,\&quot;isCompletaAttiva\&quot;:false,\&quot;Unibox\&quot;:\&quot;\&quot;,\&quot;Firmatario\&quot;:\&quot;\&quot;,\&quot;Rettificabile\&quot;:\&quot;\&quot;,\&quot;Titoli al legale\&quot;:\&quot;\&quot;,\&quot;Modello\&quot;:\&quot;\&quot;,\&quot;Targa\&quot;:\&quot;\&quot;,\&quot;Esposizione AR\&quot;:false,\&quot;Omnicanalità\&quot;:\&quot;\&quot;,\&quot;Frazionamento\&quot;:\&quot;MENSILE\&quot;,\&quot;N. Folder\&quot;:\&quot;101853000247030\&quot;,\&quot;Premio\&quot;:0,\&quot;Scadenza\&quot;:\&quot;2025-06-27T22:00:00\&quot;,\&quot;Numero Polizza/Posizione\&quot;:[192139688],\&quot;Ambito\&quot;:\&quot;\&quot;,\&quot;Tipo\&quot;:\&quot;4\&quot;,\&quot;Società\&quot;:\&quot;1\&quot;},{\&quot;uplCrmQuietId\&quot;:\&quot;\&quot;,\&quot;idContrattoPTF\&quot;:\&quot;7ae53956-c050-4cbd-bab6-ff71460b0aaa\&quot;,\&quot;xquietanzaId\&quot;:\&quot;213622148\&quot;,\&quot;contId\&quot;:\&quot;922339426762273302\&quot;,\&quot;isFEAAttiva\&quot;:false,\&quot;isFEAVisualizzata\&quot;:true,\&quot;isProdottoUnico\&quot;:true,\&quot;isFirmaAttiva\&quot;:true,\&quot;isRettificaAttiva\&quot;:true,\&quot;isIncassaAttiva\&quot;:true,\&quot;isCompletaAttiva\&quot;:false,\&quot;Unibox\&quot;:\&quot;\&quot;,\&quot;Firmatario\&quot;:\&quot;\&quot;,\&quot;Rettificabile\&quot;:\&quot;\&quot;,\&quot;Titoli al legale\&quot;:\&quot;\&quot;,\&quot;Modello\&quot;:\&quot;\&quot;,\&quot;Targa\&quot;:\&quot;\&quot;,\&quot;Esposizione AR\&quot;:false,\&quot;Omnicanalità\&quot;:\&quot;\&quot;,\&quot;Frazionamento\&quot;:\&quot;MENSILE\&quot;,\&quot;N. Folder\&quot;:\&quot;101853000246509\&quot;,\&quot;Premio\&quot;:0,\&quot;Scadenza\&quot;:\&quot;2025-06-27T22:00:00\&quot;,\&quot;Numero Polizza/Posizione\&quot;:[192139230],\&quot;Ambito\&quot;:\&quot;\&quot;,\&quot;Tipo\&quot;:\&quot;4\&quot;,\&quot;Società\&quot;:\&quot;1\&quot;},{\&quot;uplCrmQuietId\&quot;:\&quot;\&quot;,\&quot;idContrattoPTF\&quot;:\&quot;e9576864-0dee-4bbc-8565-f1cc2c00db79\&quot;,\&quot;xquietanzaId\&quot;:\&quot;213622267\&quot;,\&quot;contId\&quot;:\&quot;922339426762273302\&quot;,\&quot;isFEAAttiva\&quot;:false,\&quot;isFEAVisualizzata\&quot;:true,\&quot;isProdottoUnico\&quot;:true,\&quot;isFirmaAttiva\&quot;:true,\&quot;isRettificaAttiva\&quot;:true,\&quot;isIncassaAttiva\&quot;:true,\&quot;isCompletaAttiva\&quot;:false,\&quot;Unibox\&quot;:\&quot;\&quot;,\&quot;Firmatario\&quot;:\&quot;\&quot;,\&quot;Rettificabile\&quot;:\&quot;\&quot;,\&quot;Titoli al legale\&quot;:\&quot;\&quot;,\&quot;Modello\&quot;:\&quot;\&quot;,\&quot;Targa\&quot;:\&quot;\&quot;,\&quot;Esposizione AR\&quot;:false,\&quot;Omnicanalità\&quot;:\&quot;\&quot;,\&quot;Frazionamento\&quot;:\&quot;MENSILE\&quot;,\&quot;N. Folder\&quot;:\&quot;101853000247687\&quot;,\&quot;Premio\&quot;:0,\&quot;Scadenza\&quot;:\&quot;2025-06-29T22:00:00\&quot;,\&quot;Numero Polizza/Posizione\&quot;:[192140242],\&quot;Ambito\&quot;:\&quot;\&quot;,\&quot;Tipo\&quot;:\&quot;4\&quot;,\&quot;Società\&quot;:\&quot;1\&quot;},{\&quot;uplCrmQuietId\&quot;:\&quot;\&quot;,\&quot;idContrattoPTF\&quot;:\&quot;7359394b-c6e5-4779-872a-e819f9533350\&quot;,\&quot;xquietanzaId\&quot;:\&quot;213621742\&quot;,\&quot;contId\&quot;:\&quot;922339426762273302\&quot;,\&quot;isFEAAttiva\&quot;:false,\&quot;isFEAVisualizzata\&quot;:true,\&quot;isProdottoUnico\&quot;:true,\&quot;isFirmaAttiva\&quot;:true,\&quot;isRettificaAttiva\&quot;:true,\&quot;isIncassaAttiva\&quot;:true,\&quot;isCompletaAttiva\&quot;:false,\&quot;Unibox\&quot;:\&quot;\&quot;,\&quot;Firmatario\&quot;:\&quot;\&quot;,\&quot;Rettificabile\&quot;:\&quot;\&quot;,\&quot;Titoli al legale\&quot;:\&quot;\&quot;,\&quot;Modello\&quot;:\&quot;\&quot;,\&quot;Targa\&quot;:\&quot;\&quot;,\&quot;Esposizione AR\&quot;:false,\&quot;Omnicanalità\&quot;:\&quot;\&quot;,\&quot;Frazionamento\&quot;:\&quot;MENSILE\&quot;,\&quot;N. Folder\&quot;:\&quot;101853000212733\&quot;,\&quot;Premio\&quot;:0,\&quot;Scadenza\&quot;:\&quot;2025-07-20T22:00:00\&quot;,\&quot;Numero Polizza/Posizione\&quot;:[192135424],\&quot;Ambito\&quot;:\&quot;\&quot;,\&quot;Tipo\&quot;:\&quot;4\&quot;,\&quot;Società\&quot;:\&quot;1\&quot;},{\&quot;uplCrmQuietId\&quot;:\&quot;\&quot;,\&quot;idContrattoPTF\&quot;:\&quot;f5d270eb-0feb-4083-b366-60b7aaa726cd\&quot;,\&quot;xquietanzaId\&quot;:\&quot;213631528\&quot;,\&quot;contId\&quot;:\&quot;922339426762273302\&quot;,\&quot;isFEAAttiva\&quot;:false,\&quot;isFEAVisualizzata\&quot;:true,\&quot;isProdottoUnico\&quot;:true,\&quot;isFirmaAttiva\&quot;:true,\&quot;isRettificaAttiva\&quot;:true,\&quot;isIncassaAttiva\&quot;:true,\&quot;isCompletaAttiva\&quot;:false,\&quot;Unibox\&quot;:\&quot;\&quot;,\&quot;Firmatario\&quot;:\&quot;\&quot;,\&quot;Rettificabile\&quot;:\&quot;\&quot;,\&quot;Titoli al legale\&quot;:\&quot;\&quot;,\&quot;Modello\&quot;:\&quot;\&quot;,\&quot;Targa\&quot;:\&quot;\&quot;,\&quot;Esposizione AR\&quot;:false,\&quot;Omnicanalità\&quot;:\&quot;\&quot;,\&quot;Frazionamento\&quot;:\&quot;MENSILE\&quot;,\&quot;N. Folder\&quot;:\&quot;101853000292438\&quot;,\&quot;Premio\&quot;:0,\&quot;Scadenza\&quot;:\&quot;2025-07-23T22:00:00\&quot;,\&quot;Numero Polizza/Posizione\&quot;:[194024365],\&quot;Ambito\&quot;:\&quot;\&quot;,\&quot;Tipo\&quot;:\&quot;4\&quot;,\&quot;Società\&quot;:\&quot;1\&quot;},{\&quot;uplCrmQuietId\&quot;:\&quot;\&quot;,\&quot;idContrattoPTF\&quot;:\&quot;c783733c-0eba-4cdc-93ff-ef5338436e1d\&quot;,\&quot;xquietanzaId\&quot;:\&quot;213631520\&quot;,\&quot;contId\&quot;:\&quot;922339426762273302\&quot;,\&quot;isFEAAttiva\&quot;:false,\&quot;isFEAVisualizzata\&quot;:true,\&quot;isProdottoUnico\&quot;:true,\&quot;isFirmaAttiva\&quot;:true,\&quot;isRettificaAttiva\&quot;:true,\&quot;isIncassaAttiva\&quot;:true,\&quot;isCompletaAttiva\&quot;:false,\&quot;Unibox\&quot;:\&quot;\&quot;,\&quot;Firmatario\&quot;:\&quot;\&quot;,\&quot;Rettificabile\&quot;:\&quot;\&quot;,\&quot;Titoli al legale\&quot;:\&quot;\&quot;,\&quot;Modello\&quot;:\&quot;\&quot;,\&quot;Targa\&quot;:\&quot;\&quot;,\&quot;Esposizione AR\&quot;:false,\&quot;Omnicanalità\&quot;:\&quot;\&quot;,\&quot;Frazionamento\&quot;:\&quot;MENSILE\&quot;,\&quot;N. Folder\&quot;:\&quot;101853000292426\&quot;,\&quot;Premio\&quot;:0,\&quot;Scadenza\&quot;:\&quot;2025-07-23T22:00:00\&quot;,\&quot;Numero Polizza/Posizione\&quot;:[192165386],\&quot;Ambito\&quot;:\&quot;\&quot;,\&quot;Tipo\&quot;:\&quot;4\&quot;,\&quot;Società\&quot;:\&quot;1\&quot;},{\&quot;uplCrmQuietId\&quot;:\&quot;\&quot;,\&quot;idContrattoPTF\&quot;:\&quot;fc6fc21a-9ba6-413c-894c-fb00b04ea34a\&quot;,\&quot;xquietanzaId\&quot;:\&quot;213622220\&quot;,\&quot;contId\&quot;:\&quot;922339426762273302\&quot;,\&quot;isFEAAttiva\&quot;:false,\&quot;isFEAVisualizzata\&quot;:true,\&quot;isProdottoUnico\&quot;:true,\&quot;isFirmaAttiva\&quot;:true,\&quot;isRettificaAttiva\&quot;:true,\&quot;isIncassaAttiva\&quot;:true,\&quot;isCompletaAttiva\&quot;:false,\&quot;Unibox\&quot;:\&quot;\&quot;,\&quot;Firmatario\&quot;:\&quot;\&quot;,\&quot;Rettificabile\&quot;:\&quot;\&quot;,\&quot;Titoli al legale\&quot;:\&quot;\&quot;,\&quot;Modello\&quot;:\&quot;\&quot;,\&quot;Targa\&quot;:\&quot;\&quot;,\&quot;Esposizione AR\&quot;:false,\&quot;Omnicanalità\&quot;:\&quot;\&quot;,\&quot;Frazionamento\&quot;:\&quot;MENSILE\&quot;,\&quot;N. Folder\&quot;:\&quot;101853000247030\&quot;,\&quot;Premio\&quot;:0,\&quot;Scadenza\&quot;:\&quot;2025-07-27T22:00:00\&quot;,\&quot;Numero Polizza/Posizione\&quot;:[192139688],\&quot;Ambito\&quot;:\&quot;\&quot;,\&quot;Tipo\&quot;:\&quot;4\&quot;,\&quot;Società\&quot;:\&quot;1\&quot;},{\&quot;uplCrmQuietId\&quot;:\&quot;\&quot;,\&quot;idContrattoPTF\&quot;:\&quot;7ae53956-c050-4cbd-bab6-ff71460b0aaa\&quot;,\&quot;xquietanzaId\&quot;:\&quot;213622141\&quot;,\&quot;contId\&quot;:\&quot;922339426762273302\&quot;,\&quot;isFEAAttiva\&quot;:false,\&quot;isFEAVisualizzata\&quot;:true,\&quot;isProdottoUnico\&quot;:true,\&quot;isFirmaAttiva\&quot;:true,\&quot;isRettificaAttiva\&quot;:true,\&quot;isIncassaAttiva\&quot;:true,\&quot;isCompletaAttiva\&quot;:false,\&quot;Unibox\&quot;:\&quot;\&quot;,\&quot;Firmatario\&quot;:\&quot;\&quot;,\&quot;Rettificabile\&quot;:\&quot;\&quot;,\&quot;Titoli al legale\&quot;:\&quot;\&quot;,\&quot;Modello\&quot;:\&quot;\&quot;,\&quot;Targa\&quot;:\&quot;\&quot;,\&quot;Esposizione AR\&quot;:false,\&quot;Omnicanalità\&quot;:\&quot;\&quot;,\&quot;Frazionamento\&quot;:\&quot;MENSILE\&quot;,\&quot;N. Folder\&quot;:\&quot;101853000246509\&quot;,\&quot;Premio\&quot;:0,\&quot;Scadenza\&quot;:\&quot;2025-07-27T22:00:00\&quot;,\&quot;Numero Polizza/Posizione\&quot;:[192139230],\&quot;Ambito\&quot;:\&quot;\&quot;,\&quot;Tipo\&quot;:\&quot;4\&quot;,\&quot;Società\&quot;:\&quot;1\&quot;}]&apos;)&quot;
  },
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;label&quot; : &quot;SetValues3&quot;,
  &quot;responseJSONNode&quot; : &quot;respUnipol&quot;,
  &quot;responseJSONPath&quot; : &quot;responseUnipol&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
        <sequenceNumber>13.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>MockResponseUnisalute</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;elementValueMap&quot; : {
    &quot;responseUnisalute&quot; : &quot;=DESERIALIZE(&apos;[\n  {\n    \&quot;uplCrmQuietId\&quot;: \&quot;\&quot;,\n    \&quot;idContrattoPTF\&quot;: \&quot;f5d270eb-0feb-4083-b366-60b7aaa726cd\&quot;,\n    \&quot;xquietanzaId\&quot;: \&quot;213631529\&quot;,\n    \&quot;contId\&quot;: \&quot;922339426762273302\&quot;,\n    \&quot;isFEAAttiva\&quot;: false,\n    \&quot;isFEAVisualizzata\&quot;: true,\n    \&quot;isProdottoUnico\&quot;: true,\n    \&quot;isFirmaAttiva\&quot;: true,\n    \&quot;isRettificaAttiva\&quot;: true,\n    \&quot;isIncassaAttiva\&quot;: true,\n    \&quot;isCompletaAttiva\&quot;: false,\n    \&quot;Unibox\&quot;: \&quot;\&quot;,\n    \&quot;Firmatario\&quot;: \&quot;\&quot;,\n    \&quot;Rettificabile\&quot;: \&quot;\&quot;,\n    \&quot;Titoli al legale\&quot;: \&quot;\&quot;,\n    \&quot;Modello\&quot;: \&quot;\&quot;,\n    \&quot;Targa\&quot;: \&quot;\&quot;,\n    \&quot;Esposizione AR\&quot;: false,\n    \&quot;Omnicanalità\&quot;: \&quot;\&quot;,\n    \&quot;Frazionamento\&quot;: \&quot;MENSILE\&quot;,\n    \&quot;N. Folder\&quot;: \&quot;101853000292438\&quot;,\n    \&quot;Premio\&quot;: 0,\n    \&quot;Scadenza\&quot;: \&quot;2025-06-23T22:00:00\&quot;,\n    \&quot;Numero Polizza/Posizione\&quot;: [\n      19402498365\n    ],\n    \&quot;Ambito\&quot;: \&quot;\&quot;,\n    \&quot;Tipo\&quot;: \&quot;4\&quot;,\n    \&quot;Società\&quot;: \&quot;4\&quot;\n  },\n  {\n    \&quot;uplCrmQuietId\&quot;: \&quot;\&quot;,\n    \&quot;idContrattoPTF\&quot;: \&quot;c783733c-0eba-4cdc-93ff-ef5338436e1d\&quot;,\n    \&quot;xquietanzaId\&quot;: \&quot;213631525\&quot;,\n    \&quot;contId\&quot;: \&quot;922339426762273302\&quot;,\n    \&quot;isFEAAttiva\&quot;: false,\n    \&quot;isFEAVisualizzata\&quot;: true,\n    \&quot;isProdottoUnico\&quot;: true,\n    \&quot;isFirmaAttiva\&quot;: true,\n    \&quot;isRettificaAttiva\&quot;: true,\n    \&quot;isIncassaAttiva\&quot;: true,\n    \&quot;isCompletaAttiva\&quot;: false,\n    \&quot;Unibox\&quot;: \&quot;\&quot;,\n    \&quot;Firmatario\&quot;: \&quot;\&quot;,\n    \&quot;Rettificabile\&quot;: \&quot;\&quot;,\n    \&quot;Titoli al legale\&quot;: \&quot;\&quot;,\n    \&quot;Modello\&quot;: \&quot;\&quot;,\n    \&quot;Targa\&quot;: \&quot;\&quot;,\n    \&quot;Esposizione AR\&quot;: false,\n    \&quot;Omnicanalità\&quot;: \&quot;\&quot;,\n    \&quot;Frazionamento\&quot;: \&quot;MENSILE\&quot;,\n    \&quot;N. Folder\&quot;: \&quot;101853000292426\&quot;,\n    \&quot;Premio\&quot;: 0,\n    \&quot;Scadenza\&quot;: \&quot;2025-06-23T22:00:00\&quot;,\n    \&quot;Numero Polizza/Posizione\&quot;: [\n      1999885386\n    ],\n    \&quot;Ambito\&quot;: \&quot;\&quot;,\n    \&quot;Tipo\&quot;: \&quot;4\&quot;,\n    \&quot;Società\&quot;: \&quot;4\&quot;\n  },\n  {\n    \&quot;uplCrmQuietId\&quot;: \&quot;\&quot;,\n    \&quot;idContrattoPTF\&quot;: \&quot;fc6fc21a-9ba6-413c-894c-fb00b04ea34a\&quot;,\n    \&quot;xquietanzaId\&quot;: \&quot;213622222\&quot;,\n    \&quot;contId\&quot;: \&quot;922339426762273302\&quot;,\n    \&quot;isFEAAttiva\&quot;: false,\n    \&quot;isFEAVisualizzata\&quot;: true,\n    \&quot;isProdottoUnico\&quot;: true,\n    \&quot;isFirmaAttiva\&quot;: true,\n    \&quot;isRettificaAttiva\&quot;: true,\n    \&quot;isIncassaAttiva\&quot;: true,\n    \&quot;isCompletaAttiva\&quot;: false,\n    \&quot;Unibox\&quot;: \&quot;\&quot;,\n    \&quot;Firmatario\&quot;: \&quot;\&quot;,\n    \&quot;Rettificabile\&quot;: \&quot;\&quot;,\n    \&quot;Titoli al legale\&quot;: \&quot;\&quot;,\n    \&quot;Modello\&quot;: \&quot;\&quot;,\n    \&quot;Targa\&quot;: \&quot;\&quot;,\n    \&quot;Esposizione AR\&quot;: false,\n    \&quot;Omnicanalità\&quot;: \&quot;\&quot;,\n    \&quot;Frazionamento\&quot;: \&quot;MENSILE\&quot;,\n    \&quot;N. Folder\&quot;: \&quot;101853000247030\&quot;,\n    \&quot;Premio\&quot;: 0,\n    \&quot;Scadenza\&quot;: \&quot;2025-06-27T22:00:00\&quot;,\n    \&quot;Numero Polizza/Posizione\&quot;: [\n      192632688\n    ],\n    \&quot;Ambito\&quot;: \&quot;\&quot;,\n    \&quot;Tipo\&quot;: \&quot;4\&quot;,\n    \&quot;Società\&quot;: \&quot;4\&quot;\n  }\n]&apos;)&quot;
  },
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;label&quot; : &quot;SetValues3&quot;,
  &quot;responseJSONNode&quot; : &quot;respUnisalute&quot;,
  &quot;responseJSONPath&quot; : &quot;responseUnisalute&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
        <sequenceNumber>14.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>NetworkUserExtractUnipol</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;bundle&quot; : &quot;NetworkUserExtract&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;element&quot; : &quot;UserId&quot;,
    &quot;inputParam&quot; : &quot;UserId&quot;
  }, {
    &quot;element&quot; : &quot;&apos;SOC_1&apos;&quot;,
    &quot;inputParam&quot; : &quot;societa&quot;
  } ],
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;ignoreCache&quot; : false,
  &quot;label&quot; : &quot;DataMapperExtractAction2&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;useFormulas&quot; : true
}</propertySetConfig>
        <sequenceNumber>5.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>NetworkUserExtractUnisalute</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;bundle&quot; : &quot;NetworkUserExtract&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;element&quot; : &quot;UserId&quot;,
    &quot;inputParam&quot; : &quot;UserId&quot;
  }, {
    &quot;element&quot; : &quot;&apos;SOC_4&apos;&quot;,
    &quot;inputParam&quot; : &quot;societa&quot;
  } ],
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;ignoreCache&quot; : false,
  &quot;label&quot; : &quot;DataMapperExtractAction2&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;useFormulas&quot; : true
}</propertySetConfig>
        <sequenceNumber>6.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <childElements>
                <description>=IF(OR(FILTER(LIST(respUnipol), &apos;Message=&quot;Non ci sono titoli&quot;&apos;,FILTER(LIST(respUnipol:error), &apos;errorMessage!=&quot;&quot;&apos;))),0,LISTSIZE(respUnipol))</description>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>SetContatoretoZero</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;conta&quot; : 0
  },
  &quot;responseJSONPath&quot; : &quot;conta&quot;,
  &quot;responseJSONNode&quot; : &quot;Contatore&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;label&quot; : &quot;SetValues2&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
                <sequenceNumber>30.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponsesKO</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;%SetConditions:contatoreUnipol% == 0 &amp;&amp; %SetConditions:checkMessageUnipol% == \&quot;KO\&quot;&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;label&quot; : &quot;ConditionalBlock1&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
            <sequenceNumber>29.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ResponsesKO_FirstCondition</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;%SetConditions:contatoreUnisalute% == 0 &amp;&amp; %SetConditions:checkMessageUnisalute% == \&quot;KO\&quot;&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;label&quot; : &quot;ConditionalBlock6&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
        <sequenceNumber>28.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <childElements>
                <description>=IF(OR(FILTER(LIST(respUnipol), &apos;Message=&quot;Non ci sono titoli&quot;&apos;,FILTER(LIST(respUnipol:error), &apos;errorMessage!=&quot;&quot;&apos;))),0,LISTSIZE(respUnipol))</description>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>Contatore</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;elementValueMap&quot; : {
    &quot;conta&quot; : &quot;=IF(OR(finalResponse:Message == \&quot;Non ci sono titoli\&quot;,ISNOTBLANK(finalResponse:error:errorMessage)),0, LISTSIZE(finalResponse))&quot;
  },
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;label&quot; : &quot;SetValues2&quot;,
  &quot;responseJSONNode&quot; : &quot;Contatore&quot;,
  &quot;responseJSONPath&quot; : &quot;conta&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
                <sequenceNumber>27.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>MergeResponses</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;advancedMerge&quot; : false,
  &quot;advancedMergeMap&quot; : [ ],
  &quot;allowMergeNulls&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;dynamicOutputFields&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;filterListFormula&quot; : &quot;&quot;,
  &quot;hasPrimary&quot; : false,
  &quot;label&quot; : &quot;ListAction1&quot;,
  &quot;mergeFields&quot; : [ ],
  &quot;mergeListsOrder&quot; : [ &quot;respUnipol&quot;, &quot;respUnisalute&quot; ],
  &quot;preventIntraListMerge&quot; : false,
  &quot;primaryListKey&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;finalResponse&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;sortBy&quot; : [ ],
  &quot;sortInDescendingOrder&quot; : false,
  &quot;updateFieldValue&quot; : { },
  &quot;useFormulas&quot; : true
}</propertySetConfig>
                <sequenceNumber>26.0</sequenceNumber>
                <type>List Merge Action</type>
            </childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponsesOK</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;%SetConditions:contatoreUnisalute% &gt; 0 &amp;&amp; %SetConditions:checkMessageUnisalute% == \&quot;OK\&quot;&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;label&quot; : &quot;ConditionalBlock1&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
            <sequenceNumber>25.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ResponsesOK_FirstCondition</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;%SetConditions:contatoreUnipol% &gt; 0 &amp;&amp; %SetConditions:checkMessageUnipol% == \&quot;OK\&quot;&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;label&quot; : &quot;ConditionalBlock5&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
        <sequenceNumber>24.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <childElements>
                <description>=IF(OR(FILTER(LIST(respUnipol), &apos;Message=&quot;Non ci sono titoli&quot;&apos;,FILTER(LIST(respUnipol:error), &apos;errorMessage!=&quot;&quot;&apos;))),0,LISTSIZE(respUnipol))</description>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>SetContatoreUnipol</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;elementValueMap&quot; : {
    &quot;conta&quot; : &quot;=IF(OR(respUnipol:Message == \&quot;Non ci sono titoli\&quot;,ISNOTBLANK(respUnipol:error:errorMessage)),0, LISTSIZE(respUnipol))&quot;
  },
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;label&quot; : &quot;SetValues2&quot;,
  &quot;responseJSONNode&quot; : &quot;Contatore&quot;,
  &quot;responseJSONPath&quot; : &quot;conta&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
                <sequenceNumber>19.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>SetFinalResponseUnipol</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;elementValueMap&quot; : {
    &quot;UnipolResponse&quot; : &quot;%respUnipol%&quot;
  },
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;label&quot; : &quot;SetValues7&quot;,
  &quot;responseJSONNode&quot; : &quot;finalResponse&quot;,
  &quot;responseJSONPath&quot; : &quot;UnipolResponse&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
                <sequenceNumber>18.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseUnipolOK_ResponseUnisaluteKO</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;%SetConditions:contatoreUnipol% &gt; 0 &amp;&amp; %SetConditions:checkMessageUnipol% == \&quot;OK\&quot;&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;label&quot; : &quot;ConditionalBlock1&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
            <sequenceNumber>17.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ResponseUnipolOK_ResponseUnisaluteKO_FirstCondition</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;%SetConditions:contatoreUnisalute% == 0 &amp;&amp; %SetConditions:checkMessageUnisalute% == \&quot;KO\&quot; &quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;label&quot; : &quot;ConditionalBlock11&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
        <sequenceNumber>16.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <childElements>
                <description>=IF(OR(FILTER(LIST(respUnipol), &apos;Message=&quot;Non ci sono titoli&quot;&apos;,FILTER(LIST(respUnipol:error), &apos;errorMessage!=&quot;&quot;&apos;))),0,LISTSIZE(respUnipol))</description>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>SetContatoreUnisalute</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;elementValueMap&quot; : {
    &quot;conta&quot; : &quot;=IF(OR(respUnisalute:Message == \&quot;Non ci sono titoli\&quot;,ISNOTBLANK(respUnisalute:error:errorMessage)),0, LISTSIZE(respUnisalute))&quot;
  },
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;label&quot; : &quot;SetValues2&quot;,
  &quot;responseJSONNode&quot; : &quot;Contatore&quot;,
  &quot;responseJSONPath&quot; : &quot;conta&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
                <sequenceNumber>23.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>SetFinalResponseUnisalute</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;elementValueMap&quot; : {
    &quot;UnisaluteResponse&quot; : &quot;%respUnisalute%&quot;
  },
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;label&quot; : &quot;SetValues7&quot;,
  &quot;responseJSONNode&quot; : &quot;finalResponse&quot;,
  &quot;responseJSONPath&quot; : &quot;UnisaluteResponse&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
                <sequenceNumber>22.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseUnisaluteOK_ResponseUnipolKO</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;%SetConditions:contatoreUnisalute% &gt; 0 &amp;&amp; %SetConditions:checkMessageUnisalute% == \&quot;OK\&quot;&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;label&quot; : &quot;ConditionalBlock1&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
            <sequenceNumber>21.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ResponseUnisaluteOK_ResponseUnipolKO_FirstCondition</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;disOnTplt&quot; : false,
  &quot;executionConditionalFormula&quot; : &quot;%SetConditions:contatoreUnipol% == 0 &amp;&amp; %SetConditions:checkMessageUnipol% == \&quot;KO\&quot; &quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;label&quot; : &quot;ConditionalBlock12&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
        <sequenceNumber>20.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ReturnResponse</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;Contatore&quot; : &quot;%Contatore%&quot;,
    &quot;Contenziosi&quot; : &quot;%TransformResponse%&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;disOnTplt&quot; : false,
  &quot;label&quot; : &quot;ResponseAction1&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
        <sequenceNumber>32.0</sequenceNumber>
        <type>Response Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetConditions</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;elementValueMap&quot; : {
    &quot;checkMessageUnipol&quot; : &quot;=IF(ISNOTBLANK(%respUnipol:error%) || %respUnipol:Message% = \&quot;Non ci sono titoli\&quot; || ISBLANK(%respUnipol%),\&quot;KO\&quot;,\&quot;OK\&quot;)&quot;,
    &quot;checkMessageUnisalute&quot; : &quot;=IF(ISNOTBLANK(%respUnisalute:error%) || (%respUnisalute:Message% = \&quot;Non ci sono titoli\&quot; || ISBLANK(%respUnisalute%)),\&quot;KO\&quot;,\&quot;OK\&quot;)&quot;,
    &quot;contatoreUnipol&quot; : &quot;=IF(ISNOTBLANK(%respUnipol:error%) || %respUnipol:Message% = \&quot;Non ci sono titoli\&quot; || ISBLANK(%respUnipol%),0,LISTSIZE(%respUnipol%))&quot;,
    &quot;contatoreUnisalute&quot; : &quot;=IF(ISNOTBLANK(%respUnisalute:error%) || %respUnisalute:Message% = \&quot;Non ci sono titoli\&quot; || ISBLANK(%respUnisalute%),0,LISTSIZE(%respUnisalute%))&quot;
  },
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;label&quot; : &quot;SetValues14&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
        <sequenceNumber>15.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>TransformResponse</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%Contatore% &gt; 0&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : {
    &quot;IsUnipol&quot; : &quot;%getAccountTitoliInScadenza:permissionSetAssignmentUnipolSai%&quot;,
    &quot;IsUnisalute&quot; : &quot;%getAccountTitoliInScadenza:permissionSetAssignmentUnisalute%&quot;,
    &quot;Society&quot; : &quot;%getAccountTitoliInScadenza:accountSociety%&quot;,
    &quot;isAbbinato&quot; : &quot;=%isAbbinato%&quot;
  },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;finalResponse&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;TitoliInScadenzaTransform&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;label&quot; : &quot;DataMapperTransformAction1&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
        <sequenceNumber>31.0</sequenceNumber>
        <type>DataRaptor Transform Action</type>
    </omniProcessElements>
    <omniProcessKey>UniDS_Contenziosi</omniProcessKey>
    <omniProcessType>Integration Procedure</omniProcessType>
    <propertySetConfig>{
  &quot;linkToExternalObject&quot; : &quot;&quot;,
  &quot;trackingCustomData&quot; : { },
  &quot;includeAllActionsInResponse&quot; : false,
  &quot;columnsPropertyMap&quot; : [ ],
  &quot;relationshipFieldsMap&quot; : [ ],
  &quot;labelSingular&quot; : &quot;&quot;,
  &quot;labelPlural&quot; : &quot;&quot;,
  &quot;description&quot; : &quot;&quot;,
  &quot;nameColumn&quot; : &quot;&quot;,
  &quot;rollbackOnError&quot; : false,
  &quot;chainableQueriesLimit&quot; : 50,
  &quot;chainableDMLStatementsLimit&quot; : null,
  &quot;chainableCpuLimit&quot; : 2000,
  &quot;chainableHeapSizeLimit&quot; : null,
  &quot;chainableDMLRowsLimit&quot; : null,
  &quot;chainableQueryRowsLimit&quot; : null,
  &quot;chainableSoslQueriesLimit&quot; : null,
  &quot;chainableActualTimeLimit&quot; : null,
  &quot;additionalChainableResponse&quot; : { },
  &quot;queueableChainableQueriesLimit&quot; : 120,
  &quot;queueableChainableCpuLimit&quot; : 40000,
  &quot;queueableChainableHeapSizeLimit&quot; : 6,
  &quot;ttlMinutes&quot; : 5,
  &quot;mockResponseMap&quot; : { }
}</propertySetConfig>
    <subType>Contenziosi</subType>
    <type>UniDS</type>
    <uniqueName>UniDS_Contenziosi_Procedure_13</uniqueName>
    <versionNumber>13.0</versionNumber>
    <webComponentKey>38b3ba3a-3537-fd95-76a8-7b040d772b04</webComponentKey>
</OmniIntegrationProcedure>
