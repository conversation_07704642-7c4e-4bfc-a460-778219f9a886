<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <clonedFromOmniUiCardKey>Uni_SS_contenzioso/Unipolsai/2.0</clonedFromOmniUiCardKey>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;UniDS_Contenziosi&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;AccountId&quot;:&quot;{recordId}&quot;,&quot;UserId&quot;:&quot;{userId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;userId\&quot;:\&quot;{userId}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;0059X00000Jr08cQAB&quot;,&quot;id&quot;:1},{&quot;name&quot;:&quot;userId&quot;,&quot;val&quot;:&quot;0019O00000z1L5jQAE&quot;,&quot;id&quot;:8}]}}</dataSourceConfig>
    <isActive>false</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>UniContenziosi</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-14&quot;,&quot;field&quot;:&quot;Session.showOtherAgency&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;fasle&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-21&quot;,&quot;field&quot;:&quot;Session.showOtherAgency&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;}]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;bottom:x-small&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Icon&quot;,&quot;element&quot;:&quot;flexIcon&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;iconType&quot;:&quot;Salesforce SVG&quot;,&quot;iconName&quot;:&quot;standard:work_order_item&quot;,&quot;size&quot;:&quot;medium&quot;,&quot;extraclass&quot;:&quot;slds-icon_container slds-icon-standard-work-order-item &quot;,&quot;variant&quot;:&quot;inverse&quot;,&quot;imgsrc&quot;:&quot;&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#f3f3f3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#E5E5E5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;padding:16px;&quot;,&quot;class&quot;:&quot;slds-text-align_center slds-border_top slds-border_left slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#f3f3f3;     border-top: #E5E5E5 1px solid;border-left: #E5E5E5 1px solid; \n         padding:16px;&quot;},&quot;elementLabel&quot;:&quot;Block-0-Icon-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#f3f3f3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#E5E5E5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;padding:16px;&quot;,&quot;class&quot;:&quot;slds-text-align_center slds-border_top slds-border_left slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#f3f3f3;     border-top: #E5E5E5 1px solid;border-left: #E5E5E5 1px solid; \n         padding:16px;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;11&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cspan%20style=%22font-size:%2014pt;%22%3E%3Cstrong%3EContenziosi%20(%7BContatore%7D)%3C/strong%3E%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_11-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#f3f3f3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;11&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#E5E5E&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;padding:18px;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#f3f3f3;     border-top: #E5E5E 1px solid;border-right: #E5E5E 1px solid; \n         padding:18px;&quot;},&quot;elementLabel&quot;:&quot;Text-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_11-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#f3f3f3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;11&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#E5E5E&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;padding:18px;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#f3f3f3;     border-top: #E5E5E 1px solid;border-right: #E5E5E 1px solid; \n         padding:18px;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;&quot;,&quot;collapsible&quot;:true,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onclick&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1751384638138-nrdmcd34b&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1752059794874&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Navigation Item&quot;,&quot;openUrlIn&quot;:&quot;New Tab/Window&quot;,&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;Contatore&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;0&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]},&quot;Navigation Item&quot;:{&quot;targetName&quot;:&quot;Titoli_in_scadenza_legale&quot;},&quot;hasExtraParams&quot;:true,&quot;targetParams&quot;:{&quot;c__AccountId&quot;:&quot;{recordId}&quot;,&quot;c__UserId&quot;:&quot;{User.userId}&quot;}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;class&quot;:&quot;slds-card slds-border_right slds-border_bottom slds-border_left &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#E5E5E5&quot;,&quot;radius&quot;:&quot;0 0 5px 5px&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;     border-right: #E5E5E5 1px solid;border-bottom: #E5E5E5 1px solid;border-left: #E5E5E5 1px solid; \n    border-radius:0 0 5px 5px;     &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cspan%20style=%22font-size:%2010pt;%20color:%20#236fa1;%22%3E%3Cstrong%3ETitoli%20al%20legale%20(%7BContatore%7D)%3C/strong%3E%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Block-0-Text-3&quot;,&quot;key&quot;:&quot;element_element_element_block_0_0_block_1_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_1_0&quot;}],&quot;elementLabel&quot;:&quot;Block-0-Block-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;class&quot;:&quot;slds-card slds-border_right slds-border_bottom slds-border_left &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#E5E5E5&quot;,&quot;radius&quot;:&quot;0 0 5px 5px&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;     border-right: #E5E5E5 1px solid;border-bottom: #E5E5E5 1px solid;border-left: #E5E5E5 1px solid; \n    border-radius:0 0 5px 5px;     &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;UniDS_Contenziosi&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;AccountId&quot;:&quot;{recordId}&quot;,&quot;UserId&quot;:&quot;{userId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;userId\&quot;:\&quot;{userId}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;0059X00000Jr08cQAB&quot;,&quot;id&quot;:1},{&quot;name&quot;:&quot;userId&quot;,&quot;val&quot;:&quot;0019O00000z1L5jQAE&quot;,&quot;id&quot;:8}]},&quot;title&quot;:&quot;UniContenziosi&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;xmlObject&quot;:{&quot;isExplicitImport&quot;:false,&quot;targetConfigs&quot;:&quot;CiAgICAgICAgCiAgICAgICAgCiAgICAgICAgCiAgICAgICAgCiAgICA8dGFyZ2V0Q29uZmlnIHhtbG5zPSJodHRwOi8vc29hcC5zZm9yY2UuY29tLzIwMDYvMDQvbWV0YWRhdGEiIHRhcmdldHM9ImxpZ2h0bmluZ19fQXBwUGFnZSI+CiAgICAgIDxwcm9wZXJ0eSBuYW1lPSJkZWJ1ZyIgdHlwZT0iQm9vbGVhbiIvPgogICAgICA8cHJvcGVydHkgbmFtZT0icmVjb3JkSWQiIHR5cGU9IlN0cmluZyIvPgogICAgPC90YXJnZXRDb25maWc+CiAgICA8dGFyZ2V0Q29uZmlnIHhtbG5zPSJodHRwOi8vc29hcC5zZm9yY2UuY29tLzIwMDYvMDQvbWV0YWRhdGEiIHRhcmdldHM9ImxpZ2h0bmluZ19fUmVjb3JkUGFnZSI+CiAgICAgIDxwcm9wZXJ0eSBuYW1lPSJkZWJ1ZyIgdHlwZT0iQm9vbGVhbiIvPgogICAgICA8b2JqZWN0cyB4bWxucz0iIj4KICAgICAgICA8b2JqZWN0PkFjY291bnQ8L29iamVjdD4KICAgICAgPC9vYmplY3RzPgogICAgPC90YXJnZXRDb25maWc+CiAgCiAgICAgICAgCiAgICAgICAgCiAgICAgICAgCiAgICAgICAg&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__RecordPage&quot;,&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;,&quot;lightningCommunity__Page&quot;]}},&quot;xmlJson&quot;:[{&quot;@attributes&quot;:{&quot;xmlns&quot;:&quot;http://soap.sforce.com/2006/04/metadata&quot;,&quot;targets&quot;:&quot;lightning__AppPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]},{&quot;@attributes&quot;:{&quot;xmlns&quot;:&quot;http://soap.sforce.com/2006/04/metadata&quot;,&quot;targets&quot;:&quot;lightning__RecordPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}}],&quot;objects&quot;:{&quot;@attributes&quot;:{&quot;xmlns&quot;:&quot;&quot;},&quot;object&quot;:&quot;Account&quot;}}],&quot;events&quot;:[{&quot;eventname&quot;:&quot;ProdottiAgenciaEvent&quot;,&quot;channelname&quot;:&quot;ProdottiAgencia&quot;,&quot;element&quot;:&quot;action&quot;,&quot;eventtype&quot;:&quot;pubsub&quot;,&quot;recordIndex&quot;:&quot;0&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;*************-4iq9h8cnj&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-*************&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;Session.showOtherAgency&quot;,&quot;fieldValue&quot;:&quot;{action.showOtherAgency}&quot;}]},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;key&quot;:&quot;event-0&quot;,&quot;displayLabel&quot;:&quot;ProdottiAgencia:ProdottiAgenciaEvent&quot;,&quot;eventLabel&quot;:&quot;pubsub&quot;}]}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;success&quot;:false,&quot;info&quot;:{&quot;statusCode&quot;:403,&quot;status&quot;:&quot;Forbidden&quot;,&quot;Content-Type&quot;:&quot;application/json; charset=UTF-8&quot;,&quot;Date&quot;:&quot;Thu, 10 Jul 2025 12:06:53 GMT&quot;,&quot;x-correlation-id&quot;:&quot;93dbbdb5-8fcf-4a47-8350-5dee7b3331f3&quot;,&quot;Connection&quot;:&quot;Keep-Alive&quot;,&quot;Strict-Transport-Security&quot;:&quot;max-age=31536000; includeSubdomains&quot;,&quot;Transfer-Encoding&quot;:&quot;chunked&quot;},&quot;result&quot;:{&quot;timestamp&quot;:&quot;2025-07-10T12:06:53.958653033Z&quot;,&quot;correlationId&quot;:&quot;93dbbdb5-8fcf-4a47-8350-5dee7b3331f3&quot;,&quot;error&quot;:{&quot;errorPayload&quot;:{&quot;response&quot;:&quot;��\u0000\u0005sr\u0000(org.mule.extension.sqs.api.model.Message�@q\u0013R�ǧ\u0002\u0000\u0007L\u0000\u0004bodyt\u0000\u0012Ljava/lang/String;L\u0000\u000fdeduplicationIdq\u0000~\u0000\u0001L\u0000\fdelaySecondst\u0000\u0013Ljava/lang/Integer;L\u0000\u0007groupIdq\u0000~\u0000\u0001L\u0000\u0002idq\u0000~\u0000\u0001L\u0000\u0011messageAttributest\u0000\u000fLjava/util/Map;L\u0000\rreceiptHandleq\u0000~\u0000\u0001xpt\u0006�{\&quot;index\&quot;: null,\&quot;event\&quot;: {\&quot;traceId\&quot;: \&quot;53cdc52cad4c14fd0afafea9c802a931\&quot;,\&quot;key\&quot;: null,\&quot;level\&quot;: \&quot;ERROR\&quot;,\&quot;env\&quot;: \&quot;dev\&quot;,\&quot;insertDate\&quot;: \&quot;2025-07-10 12:06:53.917\&quot;,\&quot;insertDateNumber\&quot;: 20250710120653917,\&quot;user\&quot;: \&quot;test\&quot;,\&quot;serviceType\&quot;: \&quot;API\&quot;,\&quot;flowService\&quot;: \&quot;POST unipolsai-dev-s-sts-api/v1/post-token-axeuwas01\&quot;,\&quot;targetSystem\&quot;: \&quot;unipol_ms_sys_sts\&quot;,\&quot;flagReqResp\&quot;: \&quot;0\&quot;,\&quot;responseTime\&quot;: 183,\&quot;status\&quot;: null,\&quot;httpStatus\&quot;: \&quot;\&quot;,\&quot;headers\&quot;: \&quot;{\\\&quot;traceparent\\\&quot;: \\\&quot;00-53cdc52cad4c14fd0afafea9c802a931-c1ba459f0632e61e-01\\\&quot;,\\\&quot;x-anypnt-app-worker\\\&quot;: \\\&quot;14DCBCFEAD0AFF0E390946962FFDEB9F\\\&quot;,\\\&quot;x-correlation-id\\\&quot;: \\\&quot;93dbbdb5-8fcf-4a47-8350-5dee7b3331f3\\\&quot;,\\\&quot;user-agent\\\&quot;: \\\&quot;AHC/1.0\\\&quot;,\\\&quot;accept\\\&quot;: \\\&quot;*/*\\\&quot;,\\\&quot;content-type\\\&quot;: \\\&quot;application/json; charset=UTF-8\\\&quot;,\\\&quot;content-length\\\&quot;: \\\&quot;211\\\&quot;,\\\&quot;via\\\&quot;: \\\&quot;EDGE/1.1 MuleSoft\\\&quot;,\\\&quot;x-forwarded-for\\\&quot;: \\\&quot;***********\\\&quot;,\\\&quot;x-forwarded-host\\\&quot;: \\\&quot;unipolsai-dev-s-sts-api-pph8v8.internal-omxgu.deu-c1.eu1.cloudhub.io\\\&quot;,\\\&quot;x-forwarded-proto\\\&quot;: \\\&quot;https\\\&quot;,\\\&quot;host\\\&quot;: \\\&quot;unipolsai-dev-s-sts-api.88cd33c7-fe0d-4380-bc00-0af12571c27b.svc.cluster.local\\\&quot;}\&quot;,\&quot;queryParams\&quot;: \&quot;{}\&quot;,\&quot;body\&quot;: {\&quot;content\&quot;: \&quot;{\\\&quot;status\\\&quot;: 403,\\\&quot;error\\\&quot;: {\\\&quot;errorDescription\\\&quot;: \\\&quot;HTTP POST on resource &apos;https://mpgw-eur.servizi.gr-u.it/sts/v1/token&apos; failed: forbidden (403).\\\&quot;,\\\&quot;errorStatus\\\&quot;: \\\&quot;HTTP:FORBIDDEN\\\&quot;,\\\&quot;errorMessage\\\&quot;: \\\&quot;HTTP POST on resource &apos;https://mpgw-eur.servizi.gr-u.it/sts/v1/token&apos; failed: forbidden (403).\\\&quot;,\\\&quot;errorPayload\\\&quot;: {\\\&quot;response\\\&quot;: {\\\&quot;httpCode\\\&quot;: 403,\\\&quot;error\\\&quot;: 301,\\\&quot;errorMessage\\\&quot;: \\\&quot;Not found crossdomain account related to user: CN=UT-SF_TO_LEO_NP,OU=Service Account,OU=Servizio,DC=servizi,DC=gr-u,DC=it\\\&quot;}}},\\\&quot;correlationId\\\&quot;: \\\&quot;93dbbdb5-8fcf-4a47-8350-5dee7b3331f3\\\&quot;,\\\&quot;timestamp\\\&quot;: \\\&quot;2025-07-10T12:06:53.915808942Z\\\&quot;}\&quot;}}}t\u0000$0779f25c-0171-4e7f-93ae-6960ed763245pt\u0000\fgroupId-testppp&quot;},&quot;errorMessage&quot;:&quot;HTTP POST on resource &apos;https://unipolsai-dev-s-sts-api-pph8v8.internal-omxgu.deu-c1.eu1.cloudhub.io:443/api/v1/post-token-axeuwas01&apos; failed: forbidden (403).&quot;,&quot;errorStatus&quot;:&quot;HTTP:FORBIDDEN&quot;,&quot;errorDescription&quot;:&quot;HTTP POST on resource &apos;https://unipolsai-dev-s-sts-api-pph8v8.internal-omxgu.deu-c1.eu1.cloudhub.io:443/api/v1/post-token-axeuwas01&apos; failed: forbidden (403).&quot;},&quot;status&quot;:403}}</sampleDataSourceResponse>
    <versionNumber>3</versionNumber>
</OmniUiCard>
