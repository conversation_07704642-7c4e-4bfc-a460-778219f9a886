<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;RestPost_Renewals&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;period&quot;:&quot;{Parent.period}&quot;,&quot;from&quot;:&quot;{Session.InputFrom}&quot;,&quot;to&quot;:&quot;{Session.InputTo}&quot;,&quot;referent&quot;:&quot;{Session.InputReferent}&quot;,&quot;section&quot;:&quot;{Session.InputSection}&quot;,&quot;rate&quot;:&quot;{Session.InputRate}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;Session.InputPeriod\&quot;:\&quot;{Session.InputPeriod}\&quot;,\&quot;Session.InputFrom\&quot;:\&quot;{Session.InputFrom}\&quot;,\&quot;Session.InputTo\&quot;:\&quot;{Session.InputTo}\&quot;,\&quot;Session.InputRate\&quot;:\&quot;{Session.InputRate}\&quot;,\&quot;Session.InputReferent\&quot;:\&quot;{Session.InputReferent}\&quot;,\&quot;Session.InputSection\&quot;:\&quot;{Session.InputSection}\&quot;,\&quot;period\&quot;:\&quot;{period}\&quot;,\&quot;Parent.period\&quot;:\&quot;{Parent.period}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;Session.InputPeriod&quot;,&quot;val&quot;:&quot;fromTo&quot;,&quot;id&quot;:7},{&quot;name&quot;:&quot;Session.InputFrom&quot;,&quot;val&quot;:&quot;2025-02-01&quot;,&quot;id&quot;:8},{&quot;name&quot;:&quot;Session.InputTo&quot;,&quot;val&quot;:&quot;2025-03-01&quot;,&quot;id&quot;:9},{&quot;name&quot;:&quot;Session.InputRate&quot;,&quot;val&quot;:&quot;true&quot;,&quot;id&quot;:20},{&quot;name&quot;:&quot;Session.InputReferent&quot;,&quot;val&quot;:&quot;RSSMRA85T10H501Z&quot;,&quot;id&quot;:21},{&quot;name&quot;:&quot;Session.InputSection&quot;,&quot;val&quot;:&quot;RCA&quot;,&quot;id&quot;:22},{&quot;name&quot;:&quot;period&quot;,&quot;val&quot;:&quot;today&quot;,&quot;id&quot;:145},{&quot;name&quot;:&quot;Parent.period&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:146}]}}</dataSourceConfig>
    <isActive>false</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>HPRenewalDashboardChild</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;none&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Chart&quot;,&quot;element&quot;:&quot;flexChart&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;type&quot;:&quot;doughnut&quot;,&quot;hideHeader&quot;:&quot;true&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;records&quot;:&quot;{records}&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;title&quot;:&quot;Chart&quot;,&quot;channel&quot;:&quot;flexcarddesigner:chart&quot;,&quot;cutoutPercentage&quot;:0,&quot;colorPalette&quot;:&quot;&quot;,&quot;labelNode&quot;:&quot;label&quot;,&quot;valueNode&quot;:&quot;premio&quot;,&quot;maintainAspectRatio&quot;:false,&quot;aspectRatio&quot;:&quot;1&quot;,&quot;chartHeight&quot;:&quot;&quot;,&quot;displayLegend&quot;:&quot;true&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Chart-0&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;t&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;fieldName&quot;:&quot;InputPeriod&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Field-1&quot;}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;RestPost_Renewals&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;period&quot;:&quot;{Parent.period}&quot;,&quot;from&quot;:&quot;{Session.InputFrom}&quot;,&quot;to&quot;:&quot;{Session.InputTo}&quot;,&quot;referent&quot;:&quot;{Session.InputReferent}&quot;,&quot;section&quot;:&quot;{Session.InputSection}&quot;,&quot;rate&quot;:&quot;{Session.InputRate}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;Session.InputPeriod\&quot;:\&quot;{Session.InputPeriod}\&quot;,\&quot;Session.InputFrom\&quot;:\&quot;{Session.InputFrom}\&quot;,\&quot;Session.InputTo\&quot;:\&quot;{Session.InputTo}\&quot;,\&quot;Session.InputRate\&quot;:\&quot;{Session.InputRate}\&quot;,\&quot;Session.InputReferent\&quot;:\&quot;{Session.InputReferent}\&quot;,\&quot;Session.InputSection\&quot;:\&quot;{Session.InputSection}\&quot;,\&quot;period\&quot;:\&quot;{period}\&quot;,\&quot;Parent.period\&quot;:\&quot;{Parent.period}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;Session.InputPeriod&quot;,&quot;val&quot;:&quot;fromTo&quot;,&quot;id&quot;:7},{&quot;name&quot;:&quot;Session.InputFrom&quot;,&quot;val&quot;:&quot;2025-02-01&quot;,&quot;id&quot;:8},{&quot;name&quot;:&quot;Session.InputTo&quot;,&quot;val&quot;:&quot;2025-03-01&quot;,&quot;id&quot;:9},{&quot;name&quot;:&quot;Session.InputRate&quot;,&quot;val&quot;:&quot;true&quot;,&quot;id&quot;:20},{&quot;name&quot;:&quot;Session.InputReferent&quot;,&quot;val&quot;:&quot;RSSMRA85T10H501Z&quot;,&quot;id&quot;:21},{&quot;name&quot;:&quot;Session.InputSection&quot;,&quot;val&quot;:&quot;RCA&quot;,&quot;id&quot;:22},{&quot;name&quot;:&quot;period&quot;,&quot;val&quot;:&quot;today&quot;,&quot;id&quot;:145},{&quot;name&quot;:&quot;Parent.period&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:146}]},&quot;title&quot;:&quot;HPRenewalDashboardChild&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;isRepeatable&quot;:false,&quot;xmlObject&quot;:{&quot;isExplicitImport&quot;:false,&quot;targetConfigs&quot;:&quot;CiAgICAgICAgPHRhcmdldENvbmZpZyB4bWxucz0iaHR0cDovL3NvYXAuc2ZvcmNlLmNvbS8yMDA2LzA0L21ldGFkYXRhIiB0YXJnZXRzPSJsaWdodG5pbmdfX0FwcFBhZ2UiPjxwcm9wZXJ0eSBuYW1lPSJjZklucHV0UGVyaW9kIiB0eXBlPSJTdHJpbmciIGxhYmVsPSJQZXJpb2QiLz48cHJvcGVydHkgbmFtZT0iY2ZJbnB1dEZyb20iIHR5cGU9IlN0cmluZyIgbGFiZWw9IkRhIi8+PHByb3BlcnR5IG5hbWU9ImNmSW5wdXRUbyIgdHlwZT0iU3RyaW5nIiBsYWJlbD0iQSIvPjxwcm9wZXJ0eSBuYW1lPSJjZklucHV0UmVmZXJlbnQiIHR5cGU9IlN0cmluZyIgbGFiZWw9IlJlZmVyZW50Ii8+PHByb3BlcnR5IG5hbWU9ImNmSW5wdXRTZWN0aW9uIiB0eXBlPSJTdHJpbmciIGxhYmVsPSJDb21wYXJ0byIvPjxwcm9wZXJ0eSBuYW1lPSJjZklucHV0UmF0ZSIgdHlwZT0iQm9vbGVhbiIgbGFiZWw9Ik1lbnNpbGl6emF0ZSIvPjwvdGFyZ2V0Q29uZmlnPgogICAgICAgIA==&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;,&quot;lightning__RecordPage&quot;]}},&quot;sessionVars&quot;:[{&quot;name&quot;:&quot;InputPeriod&quot;,&quot;isApi&quot;:true,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:44},{&quot;name&quot;:&quot;InputFrom&quot;,&quot;isApi&quot;:true,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:45},{&quot;name&quot;:&quot;InputTo&quot;,&quot;isApi&quot;:true,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:46},{&quot;name&quot;:&quot;InputReferent&quot;,&quot;isApi&quot;:true,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:47},{&quot;name&quot;:&quot;InputSection&quot;,&quot;isApi&quot;:true,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:48},{&quot;name&quot;:&quot;InputRate&quot;,&quot;isApi&quot;:true,&quot;val&quot;:&quot;&quot;}],&quot;xmlJson&quot;:[{&quot;@attributes&quot;:{&quot;xmlns&quot;:&quot;http://soap.sforce.com/2006/04/metadata&quot;,&quot;targets&quot;:&quot;lightning__AppPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;cfInputPeriod&quot;,&quot;type&quot;:&quot;String&quot;,&quot;label&quot;:&quot;Period&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;cfInputFrom&quot;,&quot;type&quot;:&quot;String&quot;,&quot;label&quot;:&quot;Da&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;cfInputTo&quot;,&quot;type&quot;:&quot;String&quot;,&quot;label&quot;:&quot;A&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;cfInputReferent&quot;,&quot;type&quot;:&quot;String&quot;,&quot;label&quot;:&quot;Referent&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;cfInputSection&quot;,&quot;type&quot;:&quot;String&quot;,&quot;label&quot;:&quot;Comparto&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;cfInputRate&quot;,&quot;type&quot;:&quot;Boolean&quot;,&quot;label&quot;:&quot;Mensilizzate&quot;}}]}],&quot;events&quot;:[{&quot;eventname&quot;:&quot;reload&quot;,&quot;channelname&quot;:&quot;HPRenewalDashboardChild&quot;,&quot;element&quot;:&quot;action&quot;,&quot;eventtype&quot;:&quot;pubsub&quot;,&quot;recordIndex&quot;:&quot;0&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1753319037247-apy813qap&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753319037271&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;reload&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;key&quot;:&quot;event-0&quot;,&quot;displayLabel&quot;:&quot;HPRenewalDashboardChild:reload&quot;,&quot;eventLabel&quot;:&quot;pubsub&quot;}],&quot;hideChildCardPreview&quot;:false,&quot;osSupport&quot;:true}</propertySetConfig>
    <sampleDataSourceResponse>[{&quot;label&quot;:&quot;Incassati&quot;,&quot;numero&quot;:0,&quot;premio&quot;:0},{&quot;label&quot;:&quot;Non Incassati&quot;,&quot;numero&quot;:0,&quot;premio&quot;:0},{&quot;label&quot;:&quot;Totale&quot;,&quot;numero&quot;:0,&quot;premio&quot;:0}]</sampleDataSourceResponse>
    <versionNumber>1</versionNumber>
</OmniUiCard>
