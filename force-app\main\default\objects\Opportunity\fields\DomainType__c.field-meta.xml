<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>DomainType__c</fullName>
    <label>Domain Type</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>Omnicanale</fullName>
                <default>false</default>
                <label>Omnicanale</label>
            </value>
            <value>
                <fullName>Agenziale</fullName>
                <default>false</default>
                <label>Agenziale</label>
            </value>
            <value>
                <fullName>BPER_IN</fullName>
                <default>false</default>
                <label>BPER_IN</label>
            </value>
            <value>
                <fullName>BPER_OUT</fullName>
                <default>false</default>
                <label>BPER_OUT</label>
            </value>
            <value>
                <fullName>PU</fullName>
                <default>false</default>
                <label>PU</label>
            </value>
            <value>
                <fullName>ESSIG_AUTO</fullName>
                <default>false</default>
                <label>ESSIG_AUTO</label>
            </value>
            <value>
                <fullName>ESSIG_RE</fullName>
                <default>false</default>
                <label>ESSIG_RE</label>
            </value>
            <value>
                <fullName>ESSIG_VITA</fullName>
                <default>false</default>
                <label>ESSIG_VITA</label>
            </value>
            <value>
                <fullName>ESSIG_VITA_PREVIDENZA</fullName>
                <default>false</default>
                <label>Previdenza</label>
            </value>
            <value>
                <fullName>UNICA</fullName>
                <default>false</default>
                <isActive>false</isActive>
                <label>UNICA</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>
