<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <clonedFromOmniUiCardKey>FlexFatcaModifica/Unipolsai/1.0</clonedFromOmniUiCardKey>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;RestGet_Fatca&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;ActionType&quot;:&quot;PreLoadInserimentoPg&quot;,&quot;RecordId&quot;:&quot;{Parent.RecId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;Parent.RecId\&quot;:\&quot;{Parent.RecId}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:2},{&quot;name&quot;:&quot;Parent.RecId&quot;,&quot;val&quot;:&quot;a1i9X000006b31uQAA&quot;,&quot;id&quot;:8}]},&quot;state0element5block_element1_0&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;RestGet_Fatca&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;ActionType&quot;:&quot;GetOptions&quot;,&quot;SiglaProvincia&quot;:&quot;{SiglaProvincia}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;Sig\&quot;:\&quot;{Sig}\&quot;,\&quot;SigliaProvincia\&quot;:\&quot;{SigliaProvincia}\&quot;,\&quot;SiglaProvincia\&quot;:\&quot;{SiglaProvincia}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:1},{&quot;name&quot;:&quot;Sig&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:5},{&quot;name&quot;:&quot;SigliaProvincia&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:6},{&quot;name&quot;:&quot;SiglaProvincia&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:4}]},&quot;state0element5block_element2_0&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;RestGet_Fatca&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;ActionType&quot;:&quot;GetLocalita&quot;,&quot;CodiceBelfioreComune&quot;:&quot;{CodiceBelfioreComune}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;CodiceBelfioreComune\&quot;:\&quot;{CodiceBelfioreComune}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;CodiceBelfioreComune&quot;,&quot;val&quot;:&quot;F205&quot;,&quot;id&quot;:3}]},&quot;state0element10block_element1block_element0_0&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;RestPost_Fatca&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;Ciu&quot;:&quot;{Ciu}&quot;,&quot;Compagnia&quot;:&quot;{Compagnia}&quot;,&quot;CellaCensuaria&quot;:&quot;{CellaCensuaria}&quot;,&quot;IndirizzoBreve&quot;:&quot;{IndirizzoBreve}&quot;,&quot;CAP&quot;:&quot;{CAP}&quot;,&quot;CodiceBelfioreComune&quot;:&quot;{CodiceBelfioreComune}&quot;,&quot;Comune&quot;:&quot;{Comune}&quot;,&quot;FlagSedeLegale&quot;:&quot;{FlagSedeLegale}&quot;,&quot;IndirizzoCompleto&quot;:&quot;{IndirizzoCompleto}&quot;,&quot;NumeroCivico&quot;:&quot;{NumeroCivico}&quot;,&quot;Giin&quot;:&quot;{Giin}&quot;,&quot;Provincia&quot;:&quot;{Provincia}&quot;,&quot;Tin&quot;:&quot;{Tin}&quot;,&quot;TipoIndirizzo&quot;:&quot;{TipoIndirizzo}&quot;,&quot;TipologiaSocietaFatca&quot;:&quot;{TipologiaSocietaFatca}&quot;,&quot;Stato&quot;:&quot;{Stato}&quot;,&quot;SiglaProvincia&quot;:&quot;{SiglaProvincia}&quot;,&quot;ResidenzaFiscaleUsa&quot;:&quot;{ResidenzaFiscaleUsa}&quot;,&quot;ResidenzaFiscaleStatoEstero&quot;:&quot;{ResidenzaFiscaleStatoEstero}&quot;,&quot;ActionType&quot;:&quot;InserimentoFatcaPG&quot;,&quot;UserId&quot;:&quot;{User.userId}&quot;,&quot;RecordId&quot;:&quot;{Parent.RecId}&quot;,&quot;LocalitaEstera&quot;:&quot;{LocalitaEstera}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;Ciu\&quot;:\&quot;{Ciu}\&quot;,\&quot;Compagnia\&quot;:\&quot;{Compagnia}\&quot;,\&quot;CellaCensuaria\&quot;:\&quot;{CellaCensuaria}\&quot;,\&quot;IndirizzoBreve\&quot;:\&quot;{IndirizzoBreve}\&quot;,\&quot;CAP\&quot;:\&quot;{CAP}\&quot;,\&quot;CodiceBelfioreComune\&quot;:\&quot;{CodiceBelfioreComune}\&quot;,\&quot;Comune\&quot;:\&quot;{Comune}\&quot;,\&quot;FlagSedeLegale\&quot;:\&quot;{FlagSedeLegale}\&quot;,\&quot;IndirizzoCompleto\&quot;:\&quot;{IndirizzoCompleto}\&quot;,\&quot;NumeroCivico\&quot;:\&quot;{NumeroCivico}\&quot;,\&quot;Giin\&quot;:\&quot;{Giin}\&quot;,\&quot;Provincia\&quot;:\&quot;{Provincia}\&quot;,\&quot;Tin\&quot;:\&quot;{Tin}\&quot;,\&quot;TipoIndirizzo\&quot;:\&quot;{TipoIndirizzo}\&quot;,\&quot;TipologiaSocietaFatca\&quot;:\&quot;{TipologiaSocietaFatca}\&quot;,\&quot;Stato\&quot;:\&quot;{Stato}\&quot;,\&quot;SiglaProvincia\&quot;:\&quot;{SiglaProvincia}\&quot;,\&quot;ResidenzaFiscaleUsa\&quot;:\&quot;{ResidenzaFiscaleUsa}\&quot;,\&quot;ResidenzaFiscaleStatoEstero\&quot;:\&quot;{ResidenzaFiscaleStatoEstero}\&quot;,\&quot;User.userId\&quot;:\&quot;{User.userId}\&quot;,\&quot;Parent.recId\&quot;:\&quot;{Parent.recId}\&quot;,\&quot;Parent.RecId\&quot;:\&quot;{Parent.RecId}\&quot;,\&quot;LocalitaEstera\&quot;:\&quot;{LocalitaEstera}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;Ciu&quot;,&quot;val&quot;:&quot;7174861&quot;,&quot;id&quot;:1},{&quot;name&quot;:&quot;Compagnia&quot;,&quot;val&quot;:&quot;unipolsai&quot;,&quot;id&quot;:4},{&quot;name&quot;:&quot;CellaCensuaria&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:6},{&quot;name&quot;:&quot;IndirizzoBreve&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:8},{&quot;name&quot;:&quot;CAP&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:10},{&quot;name&quot;:&quot;CodiceBelfioreComune&quot;,&quot;val&quot;:&quot;F205&quot;,&quot;id&quot;:12},{&quot;name&quot;:&quot;Comune&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:14},{&quot;name&quot;:&quot;FlagSedeLegale&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:16},{&quot;name&quot;:&quot;IndirizzoCompleto&quot;,&quot;val&quot;:&quot;VIA CAPPUCCINI&quot;,&quot;id&quot;:18},{&quot;name&quot;:&quot;NumeroCivico&quot;,&quot;val&quot;:&quot;20&quot;,&quot;id&quot;:20},{&quot;name&quot;:&quot;Giin&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:22},{&quot;name&quot;:&quot;Provincia&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:24},{&quot;name&quot;:&quot;Tin&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:26},{&quot;name&quot;:&quot;TipoIndirizzo&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:28},{&quot;name&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:30},{&quot;name&quot;:&quot;Stato&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:32},{&quot;name&quot;:&quot;SiglaProvincia&quot;,&quot;val&quot;:&quot;MI&quot;,&quot;id&quot;:34},{&quot;name&quot;:&quot;ResidenzaFiscaleUsa&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:36},{&quot;name&quot;:&quot;ResidenzaFiscaleStatoEstero&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:38},{&quot;name&quot;:&quot;User.userId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:62},{&quot;name&quot;:&quot;Parent.recId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:64},{&quot;name&quot;:&quot;Parent.RecId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:89},{&quot;name&quot;:&quot;LocalitaEstera&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:45}]}}</dataSourceConfig>
    <isActive>true</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>FlexFatcaInserimento_PG</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;none&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_0_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%3Cspan%20style=%22font-size:%2014pt;%22%3EInserisci%20dati%20FATCA/CRS%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-Text-0&quot;}],&quot;elementLabel&quot;:&quot;Block-0&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cstrong%3ESede%20Amministrativa%3C/strong%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_left &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-2-Text-0-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_left &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_1_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;}],&quot;elementLabel&quot;:&quot;Block-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_2_0_baseInputElement_0_0&quot;,&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Diversa da Sede Legale&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;fieldBinding&quot;:&quot;{FlagSedeLegale}&quot;,&quot;value&quot;:&quot;&quot;,&quot;options&quot;:[{&quot;label&quot;:&quot;No&quot;,&quot;value&quot;:&quot;true&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:0},{&quot;label&quot;:&quot;Si&quot;,&quot;value&quot;:&quot;false&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:1}],&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1749047943527-m3c1nxpoi&quot;,&quot;label&quot;:&quot;RequiredIndrizzoTrue&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1749047972468&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;FlagRequiredInd&quot;,&quot;fieldValue&quot;:&quot;true&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-9&quot;,&quot;field&quot;:&quot;FlagSedeLegale&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1749048002251-e3qzjez5c&quot;,&quot;label&quot;:&quot;RequiredIndirizzoFalse&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1749048023149&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;FlagRequiredInd&quot;,&quot;fieldValue&quot;:&quot;false&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-16&quot;,&quot;field&quot;:&quot;FlagSedeLegale&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:1}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1749047943527-m3c1nxpoi&quot;,&quot;label&quot;:&quot;RequiredIndrizzoTrue&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1749047972468&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;FlagRequiredInd&quot;,&quot;fieldValue&quot;:&quot;true&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-9&quot;,&quot;field&quot;:&quot;FlagSedeLegale&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1749048002251-e3qzjez5c&quot;,&quot;label&quot;:&quot;RequiredIndirizzoFalse&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1749048023149&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;FlagRequiredInd&quot;,&quot;fieldValue&quot;:&quot;false&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-16&quot;,&quot;field&quot;:&quot;FlagSedeLegale&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:1}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;,&quot;elementLabel&quot;:&quot;Block-2-Select-0&quot;}],&quot;elementLabel&quot;:&quot;Block-2&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%7BCiu%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;Ciu&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;null&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Text-4&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%7BCompagnia%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-7&quot;,&quot;field&quot;:&quot;Compagnia&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;null&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Text-5&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-7&quot;,&quot;field&quot;:&quot;FlagSedeLegale&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_5_0_baseInputElement_0_0&quot;,&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Stato&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{Options}&quot;,&quot;id&quot;:0},{&quot;label&quot;:&quot;searchable&quot;,&quot;value&quot;:&quot;true&quot;,&quot;id&quot;:1},{&quot;label&quot;:&quot;required&quot;,&quot;value&quot;:&quot;{FlagRequiredInd}&quot;,&quot;id&quot;:2}],&quot;fieldBinding&quot;:&quot;{Stato}&quot;},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;parentElementKey&quot;:&quot;element_block_5_0&quot;,&quot;elementLabel&quot;:&quot;Block-6-Select-0&quot;},{&quot;key&quot;:&quot;element_element_block_5_0_baseInputElement_1_0&quot;,&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Province&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;options&quot;:[],&quot;customProperties&quot;:[{&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{OptionProvince}&quot;,&quot;id&quot;:0},{&quot;label&quot;:&quot;searchable&quot;,&quot;value&quot;:&quot;true&quot;,&quot;id&quot;:2},{&quot;label&quot;:&quot;required&quot;,&quot;value&quot;:&quot;{FlagRequiredInd}&quot;,&quot;id&quot;:2}],&quot;fieldBinding&quot;:&quot;{SiglaProvincia}&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1742322003205-9ipo5x2rc&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742468287311&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;RestGet_Fatca\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;ActionType\&quot;:\&quot;GetOptions\&quot;,\&quot;SiglaProvincia\&quot;:\&quot;{SiglaProvincia}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;recordId\\\&quot;:\\\&quot;{recordId}\\\&quot;,\\\&quot;Sig\\\&quot;:\\\&quot;{Sig}\\\&quot;,\\\&quot;SigliaProvincia\\\&quot;:\\\&quot;{SigliaProvincia}\\\&quot;,\\\&quot;SiglaProvincia\\\&quot;:\\\&quot;{SiglaProvincia}\\\&quot;}\&quot;,\&quot;resultVar\&quot;:\&quot;\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;recordId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:1},{\&quot;name\&quot;:\&quot;Sig\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:5},{\&quot;name\&quot;:\&quot;SigliaProvincia\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:6},{\&quot;name\&quot;:\&quot;SiglaProvincia\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:4}]}&quot;},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1742831738110-s1pt0sgnt&quot;,&quot;label&quot;:&quot;BlankField&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742833024292&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;Latitudine&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;Longitudine&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;IndirizzoCompleto&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;CAP&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;NumeroCivico&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;CellaCensuaria&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;IndirizzoBreve&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;Localita&quot;,&quot;fieldValue&quot;:&quot;-&quot;}]},&quot;actionIndex&quot;:1}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1742322003205-9ipo5x2rc&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742468287311&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;RestGet_Fatca\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;ActionType\&quot;:\&quot;GetOptions\&quot;,\&quot;SiglaProvincia\&quot;:\&quot;{SiglaProvincia}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;recordId\\\&quot;:\\\&quot;{recordId}\\\&quot;,\\\&quot;Sig\\\&quot;:\\\&quot;{Sig}\\\&quot;,\\\&quot;SigliaProvincia\\\&quot;:\\\&quot;{SigliaProvincia}\\\&quot;,\\\&quot;SiglaProvincia\\\&quot;:\\\&quot;{SiglaProvincia}\\\&quot;}\&quot;,\&quot;resultVar\&quot;:\&quot;\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;recordId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:1},{\&quot;name\&quot;:\&quot;Sig\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:5},{\&quot;name\&quot;:\&quot;SigliaProvincia\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:6},{\&quot;name\&quot;:\&quot;SiglaProvincia\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:4}]}&quot;},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1742831738110-s1pt0sgnt&quot;,&quot;label&quot;:&quot;BlankField&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742833024292&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;Latitudine&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;Longitudine&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;IndirizzoCompleto&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;CAP&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;NumeroCivico&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;CellaCensuaria&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;IndirizzoBreve&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;Localita&quot;,&quot;fieldValue&quot;:&quot;-&quot;}]},&quot;actionIndex&quot;:1}],&quot;showSpinner&quot;:&quot;false&quot;},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-9&quot;,&quot;field&quot;:&quot;Stato&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;Z000&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_5_0&quot;,&quot;elementLabel&quot;:&quot;Block-6-Select-1&quot;},{&quot;key&quot;:&quot;element_element_block_5_0_baseInputElement_2_0&quot;,&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Comuni&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;options&quot;:[],&quot;customProperties&quot;:[{&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{OptionsComuni}&quot;,&quot;id&quot;:0},{&quot;label&quot;:&quot;searchable&quot;,&quot;value&quot;:&quot;true&quot;,&quot;id&quot;:1},{&quot;label&quot;:&quot;required&quot;,&quot;value&quot;:&quot;{FlagRequiredInd}&quot;,&quot;id&quot;:2}],&quot;fieldBinding&quot;:&quot;{CodiceBelfioreComune}&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1742470343244-c2qu9ix1o&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742470890504&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;RestGet_Fatca\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;ActionType\&quot;:\&quot;GetLocalita\&quot;,\&quot;CodiceBelfioreComune\&quot;:\&quot;{CodiceBelfioreComune}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;CodiceBelfioreComune\\\&quot;:\\\&quot;{CodiceBelfioreComune}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;CodiceBelfioreComune\&quot;,\&quot;val\&quot;:\&quot;F205\&quot;,\&quot;id\&quot;:3}]}&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1742470343244-c2qu9ix1o&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742470890504&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;RestGet_Fatca\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;ActionType\&quot;:\&quot;GetLocalita\&quot;,\&quot;CodiceBelfioreComune\&quot;:\&quot;{CodiceBelfioreComune}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;CodiceBelfioreComune\\\&quot;:\\\&quot;{CodiceBelfioreComune}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;CodiceBelfioreComune\&quot;,\&quot;val\&quot;:\&quot;F205\&quot;,\&quot;id\&quot;:3}]}&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-16&quot;,&quot;field&quot;:&quot;Stato&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;Z000&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_5_0&quot;,&quot;elementLabel&quot;:&quot;Block-6-Select-2&quot;},{&quot;key&quot;:&quot;element_element_block_5_0_baseInputElement_3_0&quot;,&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Località&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;options&quot;:[],&quot;customProperties&quot;:[{&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{OptionsLocalita}&quot;,&quot;id&quot;:0},{&quot;label&quot;:&quot;searchable&quot;,&quot;value&quot;:&quot;true&quot;,&quot;id&quot;:1}],&quot;fieldBinding&quot;:&quot;{Localita}&quot;,&quot;action&quot;:null},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-23&quot;,&quot;field&quot;:&quot;Stato&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;Z000&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_5_0&quot;,&quot;elementLabel&quot;:&quot;Block-6-Select-3&quot;},{&quot;key&quot;:&quot;element_element_block_5_0_baseInputElement_4_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;CAP&quot;,&quot;fieldBinding&quot;:&quot;{CAP}&quot;,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;required&quot;,&quot;value&quot;:&quot;{FlagRequiredInd}&quot;,&quot;id&quot;:0}]},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;Stato&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;Z000&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;},&quot;parentElementKey&quot;:&quot;element_block_5_0&quot;,&quot;elementLabel&quot;:&quot;Block-6-Text-4&quot;},{&quot;key&quot;:&quot;element_element_block_5_0_baseInputElement_5_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Località&quot;,&quot;fieldBinding&quot;:&quot;{LocalitaEstera}&quot;,&quot;customProperties&quot;:[]},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;Stato&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;Z000&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_5_0&quot;,&quot;elementLabel&quot;:&quot;Block-6-Text-5&quot;},{&quot;key&quot;:&quot;element_element_block_5_0_baseInputElement_6_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Indirizzo&quot;,&quot;fieldBinding&quot;:&quot;{IndirizzoCompleto}&quot;,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;required&quot;,&quot;value&quot;:&quot;{FlagRequiredInd}&quot;,&quot;id&quot;:0}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;},&quot;parentElementKey&quot;:&quot;element_block_5_0&quot;,&quot;elementLabel&quot;:&quot;Block-6-Text-6&quot;},{&quot;key&quot;:&quot;element_element_block_5_0_baseInputElement_7_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;NR&quot;,&quot;fieldBinding&quot;:&quot;{NumeroCivico}&quot;,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;required&quot;,&quot;value&quot;:&quot;{FlagRequiredInd}&quot;,&quot;id&quot;:0}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;},&quot;parentElementKey&quot;:&quot;element_block_5_0&quot;,&quot;elementLabel&quot;:&quot;Block-6-Text-7&quot;},{&quot;key&quot;:&quot;element_element_block_5_0_block_8_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;class&quot;:&quot;slds-text-align_right slds-p-around_small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[],&quot;parentElementKey&quot;:&quot;element_block_5_0&quot;,&quot;elementLabel&quot;:&quot;Block-6-Block-8&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;class&quot;:&quot;slds-text-align_right slds-p-around_small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}],&quot;elementLabel&quot;:&quot;Block-6&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_9_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cstrong%3EDati%20FATCA/CRS%3C/strong%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_left &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_9_0&quot;,&quot;elementLabel&quot;:&quot;Block-4-Text-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_left &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}],&quot;elementLabel&quot;:&quot;Block-9&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_5_0_baseInputElement_0_0&quot;,&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Tipologia Soc. FATCA/CRS &quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;fieldBinding&quot;:&quot;{TipologiaSocietaFatca}&quot;,&quot;value&quot;:&quot;&quot;,&quot;options&quot;:[{&quot;label&quot;:&quot;-&quot;,&quot;value&quot;:&quot;0&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:0},{&quot;label&quot;:&quot;Limited FFI&quot;,&quot;value&quot;:&quot;1&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:1},{&quot;label&quot;:&quot;NPFI&quot;,&quot;value&quot;:&quot;2&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:2},{&quot;label&quot;:&quot;Passive NFFE&quot;,&quot;value&quot;:&quot;3&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:3},{&quot;label&quot;:&quot;Active NFFE&quot;,&quot;value&quot;:&quot;4&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:4},{&quot;label&quot;:&quot;Specified US Person&quot;,&quot;value&quot;:&quot;5&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:5},{&quot;label&quot;:&quot;Not Specified US Person&quot;,&quot;value&quot;:&quot;6&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:6},{&quot;label&quot;:&quot;Direct Reporting NFFE / Sponsored Direct Reporting NFFE&quot;,&quot;value&quot;:&quot;7&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:7},{&quot;label&quot;:&quot;FI / Financial Institution&quot;,&quot;value&quot;:&quot;8&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:8},{&quot;label&quot;:&quot;Limited Branch FFI&quot;,&quot;value&quot;:&quot;9&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:9},{&quot;label&quot;:&quot;Partecipating FFI&quot;,&quot;value&quot;:&quot;10&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:10},{&quot;label&quot;:&quot;Registered Deemed Compliant FFI&quot;,&quot;value&quot;:&quot;11&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:11},{&quot;label&quot;:&quot;Certified Deemed Compliant FFI&quot;,&quot;value&quot;:&quot;12&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:12},{&quot;label&quot;:&quot;Exempt Beneficial Owners&quot;,&quot;value&quot;:&quot;13&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:13}],&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1742890395667-8zbdgagyp&quot;,&quot;label&quot;:&quot;GiinWritable&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742891693950&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;FlagGiin&quot;,&quot;fieldValue&quot;:&quot;false&quot;},{&quot;fieldName&quot;:&quot;MinLenghtGiin&quot;,&quot;fieldValue&quot;:&quot;16&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-3&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;1&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-10&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;2&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-23&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;7&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-44&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;8&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-65&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;9&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-90&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;10&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-119&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;11&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-152&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;12&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-189&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;13&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;}]}},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1742890577174-nt5m1gp3t&quot;,&quot;label&quot;:&quot;GiinNotWritable&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742891714134&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;FlagGiin&quot;,&quot;fieldValue&quot;:&quot;true&quot;},{&quot;fieldName&quot;:&quot;Giin&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;MinLenghtGiin&quot;,&quot;fieldValue&quot;:&quot;0&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-230&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;3&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-237&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;4&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-253&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;5&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-266&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;6&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;}]}},&quot;actionIndex&quot;:1}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1742890395667-8zbdgagyp&quot;,&quot;label&quot;:&quot;GiinWritable&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742891693950&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;FlagGiin&quot;,&quot;fieldValue&quot;:&quot;false&quot;},{&quot;fieldName&quot;:&quot;MinLenghtGiin&quot;,&quot;fieldValue&quot;:&quot;16&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-3&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;1&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-10&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;2&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-23&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;7&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-44&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;8&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-65&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;9&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-90&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;10&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-119&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;11&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-152&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;12&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-189&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;13&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;}]}},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1742890577174-nt5m1gp3t&quot;,&quot;label&quot;:&quot;GiinNotWritable&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742891714134&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;FlagGiin&quot;,&quot;fieldValue&quot;:&quot;true&quot;},{&quot;fieldName&quot;:&quot;Giin&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;MinLenghtGiin&quot;,&quot;fieldValue&quot;:&quot;0&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-230&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;3&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-237&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;4&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-253&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;5&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-266&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;6&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;}]}},&quot;actionIndex&quot;:1}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_5_0&quot;,&quot;elementLabel&quot;:&quot;Block-5-Select-0&quot;}],&quot;elementLabel&quot;:&quot;Block-10&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Costituzione/residenza fiscale USA&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;fieldBinding&quot;:&quot;{ResidenzaFiscaleUsa}&quot;,&quot;value&quot;:&quot;false&quot;,&quot;options&quot;:[{&quot;label&quot;:&quot;Si&quot;,&quot;value&quot;:&quot;true&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:0},{&quot;label&quot;:&quot;No&quot;,&quot;value&quot;:&quot;false&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:1}],&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1742891761214-3lu01w710&quot;,&quot;label&quot;:&quot;TinNonObbligatorio&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1748798717502&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;MinLenghtTin&quot;,&quot;fieldValue&quot;:&quot;0&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-5&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleUsa&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1742891858312-n2y3faslb&quot;,&quot;label&quot;:&quot;TinObbligatorio&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742891923632&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;MinLenghtTin&quot;,&quot;fieldValue&quot;:&quot;9&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-12&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleUsa&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:1}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1742891761214-3lu01w710&quot;,&quot;label&quot;:&quot;TinNonObbligatorio&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1748798717502&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;MinLenghtTin&quot;,&quot;fieldValue&quot;:&quot;0&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-5&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleUsa&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1742891858312-n2y3faslb&quot;,&quot;label&quot;:&quot;TinObbligatorio&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742891923632&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;MinLenghtTin&quot;,&quot;fieldValue&quot;:&quot;9&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-12&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleUsa&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:1}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;},&quot;elementLabel&quot;:&quot;Block-11-Select-0&quot;,&quot;key&quot;:&quot;element_element_block_8_0_baseInputElement_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_8_0&quot;},{&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Residenza fiscale in uno stato diverso da Italia e USA&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;fieldBinding&quot;:&quot;{ResidenzaFiscaleStatoEstero}&quot;,&quot;value&quot;:&quot;false&quot;,&quot;options&quot;:[{&quot;label&quot;:&quot;Si&quot;,&quot;value&quot;:&quot;true&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:0},{&quot;label&quot;:&quot;No&quot;,&quot;value&quot;:&quot;false&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:1}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;},&quot;elementLabel&quot;:&quot;Block-11-Select-1&quot;,&quot;key&quot;:&quot;element_element_block_8_0_baseInputElement_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_8_0&quot;},{&quot;key&quot;:&quot;element_element_block_8_0_baseInputElement_2_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;TIN USA&quot;,&quot;fieldBinding&quot;:&quot;{Tin}&quot;,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;required&quot;,&quot;value&quot;:&quot;{ResidenzaFiscaleUsa}&quot;,&quot;id&quot;:0},{&quot;label&quot;:&quot;minLength&quot;,&quot;value&quot;:&quot;{MinLenghtTin}&quot;,&quot;id&quot;:1},{&quot;label&quot;:&quot;maxLength&quot;,&quot;value&quot;:&quot;9&quot;,&quot;id&quot;:2},{&quot;label&quot;:&quot;messageWhenTooShort&quot;,&quot;value&quot;:&quot;Inserire 9 caratteri&quot;,&quot;id&quot;:3}]},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-5&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleUsa&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;}},&quot;parentElementKey&quot;:&quot;element_block_8_0&quot;,&quot;elementLabel&quot;:&quot;Block-11-Text-2&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%7BMessage%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;Status&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-7&quot;,&quot;field&quot;:&quot;ErrorType&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;2&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Block-11-Text-4-clone-0&quot;,&quot;key&quot;:&quot;element_element_block_8_0_outputField_3_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_8_0&quot;},{&quot;key&quot;:&quot;element_element_block_8_0_block_4_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-55&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;0&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-group-65&quot;,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-64&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;&lt;=&quot;,&quot;value&quot;:&quot;2&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-89&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;&gt;=&quot;,&quot;value&quot;:&quot;7&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;}],&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3EGIIN%20USA%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_left &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-11-Text-3&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_left &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_9_0_block_3_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_9_0_block_3_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Label&quot;,&quot;fieldBinding&quot;:&quot;{Giin1}&quot;,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;variant&quot;,&quot;value&quot;:&quot;label-hidden&quot;,&quot;id&quot;:0}],&quot;minLength&quot;:6,&quot;maxLength&quot;:6,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1747211913379-bbpysefkc&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1749455889906&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;Giin&quot;,&quot;fieldValue&quot;:&quot;{Giin1}.{Giin2}.{Giin3}.{Giin4}&quot;}]},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1747211913379-bbpysefkc&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1749455889906&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;Giin&quot;,&quot;fieldValue&quot;:&quot;{Giin1}.{Giin2}.{Giin3}.{Giin4}&quot;}]},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_2-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-11-Text-5&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_2-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_9_0_block_3_0_baseInputElement_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_9_0_block_3_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Label&quot;,&quot;readOnly&quot;:true,&quot;value&quot;:&quot;.&quot;,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;variant&quot;,&quot;value&quot;:&quot;label-hidden&quot;,&quot;id&quot;:0}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-11-Text-6&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_9_0_block_3_0_baseInputElement_2_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_9_0_block_3_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Label&quot;,&quot;fieldBinding&quot;:&quot;{Giin2}&quot;,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;variant&quot;,&quot;value&quot;:&quot;label-hidden&quot;,&quot;id&quot;:0}],&quot;minLength&quot;:5,&quot;maxLength&quot;:5,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1747212629189-3zdk7596e&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1749455916420&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;Giin&quot;,&quot;fieldValue&quot;:&quot;{Giin1}.{Giin2}.{Giin3}.{Giin4}&quot;}]},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1747212629189-3zdk7596e&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1749455916420&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;Giin&quot;,&quot;fieldValue&quot;:&quot;{Giin1}.{Giin2}.{Giin3}.{Giin4}&quot;}]},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_2-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-11-Text-7&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_2-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_9_0_block_3_0_baseInputElement_3_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_9_0_block_3_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Label&quot;,&quot;readOnly&quot;:true,&quot;value&quot;:&quot;.&quot;,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;variant&quot;,&quot;value&quot;:&quot;label-hidden&quot;,&quot;id&quot;:0}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-11-Text-8&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_9_0_block_3_0_baseInputElement_4_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_9_0_block_3_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Label&quot;,&quot;fieldBinding&quot;:&quot;{Giin3}&quot;,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;variant&quot;,&quot;value&quot;:&quot;label-hidden&quot;,&quot;id&quot;:0}],&quot;minLength&quot;:2,&quot;maxLength&quot;:2,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1747212659149-u1vno9k49&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1749455943123&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;Giin&quot;,&quot;fieldValue&quot;:&quot;{Giin1}.{Giin2}.{Giin3}.{Giin4}&quot;}]},&quot;isTrackingDisabled&quot;:false,&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1747212659149-u1vno9k49&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1749455943123&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;Giin&quot;,&quot;fieldValue&quot;:&quot;{Giin1}.{Giin2}.{Giin3}.{Giin4}&quot;}]},&quot;isTrackingDisabled&quot;:false,&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_2-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-11-Text-9&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_2-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_9_0_block_3_0_baseInputElement_5_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_9_0_block_3_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Label&quot;,&quot;readOnly&quot;:true,&quot;value&quot;:&quot;.&quot;,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;variant&quot;,&quot;value&quot;:&quot;label-hidden&quot;,&quot;id&quot;:0}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-11-Text-10&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_9_0_block_3_0_baseInputElement_6_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_9_0_block_3_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Label&quot;,&quot;fieldBinding&quot;:&quot;{Giin4}&quot;,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;variant&quot;,&quot;value&quot;:&quot;label-hidden&quot;,&quot;id&quot;:0}],&quot;minLength&quot;:3,&quot;maxLength&quot;:3,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1747212699108-4btop1pvg&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1749455961541&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;Giin&quot;,&quot;fieldValue&quot;:&quot;{Giin1}.{Giin2}.{Giin3}.{Giin4}&quot;}]},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1747212699108-4btop1pvg&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1749455961541&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;Giin&quot;,&quot;fieldValue&quot;:&quot;{Giin1}.{Giin2}.{Giin3}.{Giin4}&quot;}]},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_2-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-11-Text-11&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_2-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_9_0_block_3_0_baseInputElement_7_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_9_0_block_3_0&quot;}],&quot;parentElementKey&quot;:&quot;element_block_8_0&quot;,&quot;elementLabel&quot;:&quot;Block-11-Block-4&quot;},{&quot;key&quot;:&quot;element_element_block_8_0_outputField_5_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%7BMessage%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;Status&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-7&quot;,&quot;field&quot;:&quot;ErrorType&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;1&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_8_0&quot;,&quot;elementLabel&quot;:&quot;Block-11-Text-12&quot;}],&quot;elementLabel&quot;:&quot;Block-11&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;success&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_7_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%3Cstrong%3EErrore%20nel%20caricamento%20dati%3C/strong%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_7_0&quot;,&quot;elementLabel&quot;:&quot;Block-7-Text-0&quot;}],&quot;elementLabel&quot;:&quot;Block-12&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_10_0_block_0_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_block_8_0_block_0_0_action_0_0&quot;,&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Annulla&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1742897557688-3mr4r2936&quot;,&quot;label&quot;:&quot;Reset&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742897595927&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;success&quot;,&quot;fieldValue&quot;:&quot;true&quot;}]},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1742897503631-spa7dwkvu&quot;,&quot;label&quot;:&quot;CloseModal&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742897691602&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;Custom&quot;,&quot;eventName&quot;:&quot;closemodal&quot;,&quot;composed&quot;:true,&quot;bubbles&quot;:true},&quot;actionIndex&quot;:2}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;hideActionIcon&quot;:true,&quot;displayAsButton&quot;:true,&quot;buttonVariant&quot;:&quot;neutral&quot;,&quot;flyoutDetails&quot;:{}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_right &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_element_block_8_0_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-4-Block-0-Action-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_right &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}],&quot;parentElementKey&quot;:&quot;element_block_10_0&quot;,&quot;elementLabel&quot;:&quot;Block-4-Block-0&quot;},{&quot;key&quot;:&quot;element_element_block_10_0_block_1_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_block_10_0_block_1_0_action_0_0&quot;,&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Conferma&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753431498811&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;RestPost_Fatca\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;Ciu\&quot;:\&quot;{Ciu}\&quot;,\&quot;Compagnia\&quot;:\&quot;{Compagnia}\&quot;,\&quot;CellaCensuaria\&quot;:\&quot;{CellaCensuaria}\&quot;,\&quot;IndirizzoBreve\&quot;:\&quot;{IndirizzoBreve}\&quot;,\&quot;CAP\&quot;:\&quot;{CAP}\&quot;,\&quot;CodiceBelfioreComune\&quot;:\&quot;{CodiceBelfioreComune}\&quot;,\&quot;Comune\&quot;:\&quot;{Comune}\&quot;,\&quot;FlagSedeLegale\&quot;:\&quot;{FlagSedeLegale}\&quot;,\&quot;IndirizzoCompleto\&quot;:\&quot;{IndirizzoCompleto}\&quot;,\&quot;NumeroCivico\&quot;:\&quot;{NumeroCivico}\&quot;,\&quot;Giin\&quot;:\&quot;{Giin}\&quot;,\&quot;Provincia\&quot;:\&quot;{Provincia}\&quot;,\&quot;Tin\&quot;:\&quot;{Tin}\&quot;,\&quot;TipoIndirizzo\&quot;:\&quot;{TipoIndirizzo}\&quot;,\&quot;TipologiaSocietaFatca\&quot;:\&quot;{TipologiaSocietaFatca}\&quot;,\&quot;Stato\&quot;:\&quot;{Stato}\&quot;,\&quot;SiglaProvincia\&quot;:\&quot;{SiglaProvincia}\&quot;,\&quot;ResidenzaFiscaleUsa\&quot;:\&quot;{ResidenzaFiscaleUsa}\&quot;,\&quot;ResidenzaFiscaleStatoEstero\&quot;:\&quot;{ResidenzaFiscaleStatoEstero}\&quot;,\&quot;ActionType\&quot;:\&quot;InserimentoFatcaPG\&quot;,\&quot;UserId\&quot;:\&quot;{User.userId}\&quot;,\&quot;RecordId\&quot;:\&quot;{Parent.RecId}\&quot;,\&quot;LocalitaEstera\&quot;:\&quot;{LocalitaEstera}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;Ciu\\\&quot;:\\\&quot;{Ciu}\\\&quot;,\\\&quot;Compagnia\\\&quot;:\\\&quot;{Compagnia}\\\&quot;,\\\&quot;CellaCensuaria\\\&quot;:\\\&quot;{CellaCensuaria}\\\&quot;,\\\&quot;IndirizzoBreve\\\&quot;:\\\&quot;{IndirizzoBreve}\\\&quot;,\\\&quot;CAP\\\&quot;:\\\&quot;{CAP}\\\&quot;,\\\&quot;CodiceBelfioreComune\\\&quot;:\\\&quot;{CodiceBelfioreComune}\\\&quot;,\\\&quot;Comune\\\&quot;:\\\&quot;{Comune}\\\&quot;,\\\&quot;FlagSedeLegale\\\&quot;:\\\&quot;{FlagSedeLegale}\\\&quot;,\\\&quot;IndirizzoCompleto\\\&quot;:\\\&quot;{IndirizzoCompleto}\\\&quot;,\\\&quot;NumeroCivico\\\&quot;:\\\&quot;{NumeroCivico}\\\&quot;,\\\&quot;Giin\\\&quot;:\\\&quot;{Giin}\\\&quot;,\\\&quot;Provincia\\\&quot;:\\\&quot;{Provincia}\\\&quot;,\\\&quot;Tin\\\&quot;:\\\&quot;{Tin}\\\&quot;,\\\&quot;TipoIndirizzo\\\&quot;:\\\&quot;{TipoIndirizzo}\\\&quot;,\\\&quot;TipologiaSocietaFatca\\\&quot;:\\\&quot;{TipologiaSocietaFatca}\\\&quot;,\\\&quot;Stato\\\&quot;:\\\&quot;{Stato}\\\&quot;,\\\&quot;SiglaProvincia\\\&quot;:\\\&quot;{SiglaProvincia}\\\&quot;,\\\&quot;ResidenzaFiscaleUsa\\\&quot;:\\\&quot;{ResidenzaFiscaleUsa}\\\&quot;,\\\&quot;ResidenzaFiscaleStatoEstero\\\&quot;:\\\&quot;{ResidenzaFiscaleStatoEstero}\\\&quot;,\\\&quot;User.userId\\\&quot;:\\\&quot;{User.userId}\\\&quot;,\\\&quot;Parent.recId\\\&quot;:\\\&quot;{Parent.recId}\\\&quot;,\\\&quot;Parent.RecId\\\&quot;:\\\&quot;{Parent.RecId}\\\&quot;,\\\&quot;LocalitaEstera\\\&quot;:\\\&quot;{LocalitaEstera}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;Ciu\&quot;,\&quot;val\&quot;:\&quot;7174861\&quot;,\&quot;id\&quot;:1},{\&quot;name\&quot;:\&quot;Compagnia\&quot;,\&quot;val\&quot;:\&quot;unipolsai\&quot;,\&quot;id\&quot;:4},{\&quot;name\&quot;:\&quot;CellaCensuaria\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:6},{\&quot;name\&quot;:\&quot;IndirizzoBreve\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:8},{\&quot;name\&quot;:\&quot;CAP\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:10},{\&quot;name\&quot;:\&quot;CodiceBelfioreComune\&quot;,\&quot;val\&quot;:\&quot;F205\&quot;,\&quot;id\&quot;:12},{\&quot;name\&quot;:\&quot;Comune\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:14},{\&quot;name\&quot;:\&quot;FlagSedeLegale\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:16},{\&quot;name\&quot;:\&quot;IndirizzoCompleto\&quot;,\&quot;val\&quot;:\&quot;VIA CAPPUCCINI\&quot;,\&quot;id\&quot;:18},{\&quot;name\&quot;:\&quot;NumeroCivico\&quot;,\&quot;val\&quot;:\&quot;20\&quot;,\&quot;id\&quot;:20},{\&quot;name\&quot;:\&quot;Giin\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:22},{\&quot;name\&quot;:\&quot;Provincia\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:24},{\&quot;name\&quot;:\&quot;Tin\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:26},{\&quot;name\&quot;:\&quot;TipoIndirizzo\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:28},{\&quot;name\&quot;:\&quot;TipologiaSocietaFatca\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:30},{\&quot;name\&quot;:\&quot;Stato\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:32},{\&quot;name\&quot;:\&quot;SiglaProvincia\&quot;,\&quot;val\&quot;:\&quot;MI\&quot;,\&quot;id\&quot;:34},{\&quot;name\&quot;:\&quot;ResidenzaFiscaleUsa\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:36},{\&quot;name\&quot;:\&quot;ResidenzaFiscaleStatoEstero\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:38},{\&quot;name\&quot;:\&quot;User.userId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:62},{\&quot;name\&quot;:\&quot;Parent.recId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:64},{\&quot;name\&quot;:\&quot;Parent.RecId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:89},{\&quot;name\&quot;:\&quot;LocalitaEstera\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:45}]}&quot;},&quot;key&quot;:&quot;1742771490462-u2g8lwkph&quot;,&quot;label&quot;:&quot;Send&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1742897614245-r308ujzwr&quot;,&quot;label&quot;:&quot;Reload&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1748941397169&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;eventName&quot;:&quot;refreshAll&quot;,&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-7&quot;,&quot;field&quot;:&quot;success&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-17&quot;,&quot;field&quot;:&quot;isResidenzaEstero&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]},&quot;subType&quot;:&quot;PubSub&quot;,&quot;message&quot;:&quot;refresh&quot;},&quot;actionIndex&quot;:1},{&quot;key&quot;:&quot;1742897650655-wpfsil70m&quot;,&quot;label&quot;:&quot;CloseModal&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742897894159&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;Custom&quot;,&quot;eventName&quot;:&quot;closemodal&quot;,&quot;composed&quot;:true,&quot;bubbles&quot;:true,&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;success&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:2},{&quot;key&quot;:&quot;1748940947097-51awnjadc&quot;,&quot;label&quot;:&quot;OpenNewModal&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1748941336270&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;PubSub&quot;,&quot;eventName&quot;:&quot;openModify&quot;,&quot;message&quot;:&quot;openModify&quot;,&quot;hasExtraParams&quot;:true,&quot;extraParams&quot;:{&quot;RecId&quot;:&quot;{Parent.RecId}&quot;},&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;success&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-7&quot;,&quot;field&quot;:&quot;isResidenzaEstero&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;actionIndex&quot;:3}],&quot;showSpinner&quot;:&quot;true&quot;,&quot;hideActionIcon&quot;:true,&quot;displayAsButton&quot;:true,&quot;buttonVariant&quot;:&quot;brand&quot;,&quot;flyoutDetails&quot;:{},&quot;flyoutChannel&quot;:&quot;&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_left &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_element_block_10_0_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-4-Block-0-clone-0-Action-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_left &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}],&quot;parentElementKey&quot;:&quot;element_block_10_0&quot;,&quot;elementLabel&quot;:&quot;Block-4-Block-0-clone-0&quot;}],&quot;elementLabel&quot;:&quot;Block-13&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;RestGet_Fatca&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;ActionType&quot;:&quot;PreLoadInserimentoPg&quot;,&quot;RecordId&quot;:&quot;{Parent.RecId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;Parent.RecId\&quot;:\&quot;{Parent.RecId}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:2},{&quot;name&quot;:&quot;Parent.RecId&quot;,&quot;val&quot;:&quot;a1i9X000006b31uQAA&quot;,&quot;id&quot;:8}]},&quot;title&quot;:&quot;FlexFatcaInserimento_PG&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfFlexFatcaModifica_PostInsert_1_Unipolsai&quot;,&quot;Id&quot;:&quot;0Rb9O000003F85BSAS&quot;,&quot;MasterLabel&quot;:&quot;cfFlexFatcaModifica_PostInsert_1_Unipolsai&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;},&quot;dynamicCanvasWidth&quot;:{&quot;type&quot;:&quot;tablet_l&quot;},&quot;xmlObject&quot;:{&quot;masterLabel&quot;:&quot;FlexFatcaInserimento_PG&quot;,&quot;isExposed&quot;:true,&quot;apiVersion&quot;:56}}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;Ciu&quot;:&quot;33785480&quot;,&quot;OptionProvince&quot;:[{&quot;value&quot;:&quot;AG&quot;,&quot;label&quot;:&quot;AGRIGENTO&quot;},{&quot;value&quot;:&quot;AL&quot;,&quot;label&quot;:&quot;ALESSANDRIA&quot;},{&quot;value&quot;:&quot;AN&quot;,&quot;label&quot;:&quot;ANCONA&quot;},{&quot;value&quot;:&quot;AO&quot;,&quot;label&quot;:&quot;AOSTA&quot;},{&quot;value&quot;:&quot;AP&quot;,&quot;label&quot;:&quot;ASCOLI PICENO&quot;},{&quot;value&quot;:&quot;AQ&quot;,&quot;label&quot;:&quot;L&apos;AQUILA&quot;},{&quot;value&quot;:&quot;AR&quot;,&quot;label&quot;:&quot;AREZZO&quot;},{&quot;value&quot;:&quot;AT&quot;,&quot;label&quot;:&quot;ASTI&quot;},{&quot;value&quot;:&quot;AV&quot;,&quot;label&quot;:&quot;AVELLINO&quot;},{&quot;value&quot;:&quot;BA&quot;,&quot;label&quot;:&quot;BARI&quot;},{&quot;value&quot;:&quot;BG&quot;,&quot;label&quot;:&quot;BERGAMO&quot;},{&quot;value&quot;:&quot;BI&quot;,&quot;label&quot;:&quot;BIELLA&quot;},{&quot;value&quot;:&quot;BL&quot;,&quot;label&quot;:&quot;BELLUNO&quot;},{&quot;value&quot;:&quot;BN&quot;,&quot;label&quot;:&quot;BENEVENTO&quot;},{&quot;value&quot;:&quot;BO&quot;,&quot;label&quot;:&quot;BOLOGNA&quot;},{&quot;value&quot;:&quot;BR&quot;,&quot;label&quot;:&quot;BRINDISI&quot;},{&quot;value&quot;:&quot;BS&quot;,&quot;label&quot;:&quot;BRESCIA&quot;},{&quot;value&quot;:&quot;BT&quot;,&quot;label&quot;:&quot;BARLETTA-ANDRIA-TRANI&quot;},{&quot;value&quot;:&quot;BZ&quot;,&quot;label&quot;:&quot;BOLZANO&quot;},{&quot;value&quot;:&quot;CA&quot;,&quot;label&quot;:&quot;CAGLIARI&quot;},{&quot;value&quot;:&quot;CB&quot;,&quot;label&quot;:&quot;CAMPOBASSO&quot;},{&quot;value&quot;:&quot;CE&quot;,&quot;label&quot;:&quot;CASERTA&quot;},{&quot;value&quot;:&quot;CH&quot;,&quot;label&quot;:&quot;CHIETI&quot;},{&quot;value&quot;:&quot;CL&quot;,&quot;label&quot;:&quot;CALTANISSETTA&quot;},{&quot;value&quot;:&quot;CN&quot;,&quot;label&quot;:&quot;CUNEO&quot;},{&quot;value&quot;:&quot;CO&quot;,&quot;label&quot;:&quot;COMO&quot;},{&quot;value&quot;:&quot;CR&quot;,&quot;label&quot;:&quot;CREMONA&quot;},{&quot;value&quot;:&quot;CS&quot;,&quot;label&quot;:&quot;COSENZA&quot;},{&quot;value&quot;:&quot;CT&quot;,&quot;label&quot;:&quot;CATANIA&quot;},{&quot;value&quot;:&quot;CZ&quot;,&quot;label&quot;:&quot;CATANZARO&quot;},{&quot;value&quot;:&quot;EE&quot;,&quot;label&quot;:&quot;ESTERO&quot;},{&quot;value&quot;:&quot;EN&quot;,&quot;label&quot;:&quot;ENNA&quot;},{&quot;value&quot;:&quot;FC&quot;,&quot;label&quot;:&quot;FORLI&apos; CESENA&quot;},{&quot;value&quot;:&quot;FE&quot;,&quot;label&quot;:&quot;FERRARA&quot;},{&quot;value&quot;:&quot;FG&quot;,&quot;label&quot;:&quot;FOGGIA&quot;},{&quot;value&quot;:&quot;FI&quot;,&quot;label&quot;:&quot;FIRENZE&quot;},{&quot;value&quot;:&quot;FM&quot;,&quot;label&quot;:&quot;FERMO&quot;},{&quot;value&quot;:&quot;FR&quot;,&quot;label&quot;:&quot;FROSINONE&quot;},{&quot;value&quot;:&quot;GE&quot;,&quot;label&quot;:&quot;GENOVA&quot;},{&quot;value&quot;:&quot;GO&quot;,&quot;label&quot;:&quot;GORIZIA&quot;},{&quot;value&quot;:&quot;GR&quot;,&quot;label&quot;:&quot;GROSSETO&quot;},{&quot;value&quot;:&quot;IM&quot;,&quot;label&quot;:&quot;IMPERIA&quot;},{&quot;value&quot;:&quot;IS&quot;,&quot;label&quot;:&quot;ISERNIA&quot;},{&quot;value&quot;:&quot;KR&quot;,&quot;label&quot;:&quot;CROTONE&quot;},{&quot;value&quot;:&quot;LC&quot;,&quot;label&quot;:&quot;LECCO&quot;},{&quot;value&quot;:&quot;LE&quot;,&quot;label&quot;:&quot;LECCE&quot;},{&quot;value&quot;:&quot;LI&quot;,&quot;label&quot;:&quot;LIVORNO&quot;},{&quot;value&quot;:&quot;LO&quot;,&quot;label&quot;:&quot;LODI&quot;},{&quot;value&quot;:&quot;LT&quot;,&quot;label&quot;:&quot;LATINA&quot;},{&quot;value&quot;:&quot;LU&quot;,&quot;label&quot;:&quot;LUCCA&quot;},{&quot;value&quot;:&quot;MB&quot;,&quot;label&quot;:&quot;MONZA E DELLA BRIANZA&quot;},{&quot;value&quot;:&quot;MC&quot;,&quot;label&quot;:&quot;MACERATA&quot;},{&quot;value&quot;:&quot;ME&quot;,&quot;label&quot;:&quot;MESSINA&quot;},{&quot;value&quot;:&quot;MI&quot;,&quot;label&quot;:&quot;MILANO&quot;},{&quot;value&quot;:&quot;MN&quot;,&quot;label&quot;:&quot;MANTOVA&quot;},{&quot;value&quot;:&quot;MO&quot;,&quot;label&quot;:&quot;MODENA&quot;},{&quot;value&quot;:&quot;MS&quot;,&quot;label&quot;:&quot;MASSA&quot;},{&quot;value&quot;:&quot;MT&quot;,&quot;label&quot;:&quot;MATERA&quot;},{&quot;value&quot;:&quot;NA&quot;,&quot;label&quot;:&quot;NAPOLI&quot;},{&quot;value&quot;:&quot;NO&quot;,&quot;label&quot;:&quot;NOVARA&quot;},{&quot;value&quot;:&quot;NU&quot;,&quot;label&quot;:&quot;NUORO&quot;},{&quot;value&quot;:&quot;OR&quot;,&quot;label&quot;:&quot;ORISTANO&quot;},{&quot;value&quot;:&quot;PA&quot;,&quot;label&quot;:&quot;PALERMO&quot;},{&quot;value&quot;:&quot;PC&quot;,&quot;label&quot;:&quot;PIACENZA&quot;},{&quot;value&quot;:&quot;PD&quot;,&quot;label&quot;:&quot;PADOVA&quot;},{&quot;value&quot;:&quot;PE&quot;,&quot;label&quot;:&quot;PESCARA&quot;},{&quot;value&quot;:&quot;PG&quot;,&quot;label&quot;:&quot;PERUGIA&quot;},{&quot;value&quot;:&quot;PI&quot;,&quot;label&quot;:&quot;PISA&quot;},{&quot;value&quot;:&quot;PN&quot;,&quot;label&quot;:&quot;PORDENONE&quot;},{&quot;value&quot;:&quot;PO&quot;,&quot;label&quot;:&quot;PRATO&quot;},{&quot;value&quot;:&quot;PR&quot;,&quot;label&quot;:&quot;PARMA&quot;},{&quot;value&quot;:&quot;PT&quot;,&quot;label&quot;:&quot;PISTOIA&quot;},{&quot;value&quot;:&quot;PU&quot;,&quot;label&quot;:&quot;PESARO URBINO&quot;},{&quot;value&quot;:&quot;PV&quot;,&quot;label&quot;:&quot;PAVIA&quot;},{&quot;value&quot;:&quot;PZ&quot;,&quot;label&quot;:&quot;POTENZA&quot;},{&quot;value&quot;:&quot;RA&quot;,&quot;label&quot;:&quot;RAVENNA&quot;},{&quot;value&quot;:&quot;RC&quot;,&quot;label&quot;:&quot;REGGIO DI CALABRIA&quot;},{&quot;value&quot;:&quot;RE&quot;,&quot;label&quot;:&quot;REGGIO NELL&apos;EMILIA&quot;},{&quot;value&quot;:&quot;RG&quot;,&quot;label&quot;:&quot;RAGUSA&quot;},{&quot;value&quot;:&quot;RI&quot;,&quot;label&quot;:&quot;RIETI&quot;},{&quot;value&quot;:&quot;RM&quot;,&quot;label&quot;:&quot;ROMA&quot;},{&quot;value&quot;:&quot;RN&quot;,&quot;label&quot;:&quot;RIMINI&quot;},{&quot;value&quot;:&quot;RO&quot;,&quot;label&quot;:&quot;ROVIGO&quot;},{&quot;value&quot;:&quot;SA&quot;,&quot;label&quot;:&quot;SALERNO&quot;},{&quot;value&quot;:&quot;SI&quot;,&quot;label&quot;:&quot;SIENA&quot;},{&quot;value&quot;:&quot;SO&quot;,&quot;label&quot;:&quot;SONDRIO&quot;},{&quot;value&quot;:&quot;SP&quot;,&quot;label&quot;:&quot;LA SPEZIA&quot;},{&quot;value&quot;:&quot;SR&quot;,&quot;label&quot;:&quot;SIRACUSA&quot;},{&quot;value&quot;:&quot;SS&quot;,&quot;label&quot;:&quot;SASSARI&quot;},{&quot;value&quot;:&quot;SU&quot;,&quot;label&quot;:&quot;SUD SARDEGNA&quot;},{&quot;value&quot;:&quot;SV&quot;,&quot;label&quot;:&quot;SAVONA&quot;},{&quot;value&quot;:&quot;TA&quot;,&quot;label&quot;:&quot;TARANTO&quot;},{&quot;value&quot;:&quot;TE&quot;,&quot;label&quot;:&quot;TERAMO&quot;},{&quot;value&quot;:&quot;TN&quot;,&quot;label&quot;:&quot;TRENTO&quot;},{&quot;value&quot;:&quot;TO&quot;,&quot;label&quot;:&quot;TORINO&quot;},{&quot;value&quot;:&quot;TP&quot;,&quot;label&quot;:&quot;TRAPANI&quot;},{&quot;value&quot;:&quot;TR&quot;,&quot;label&quot;:&quot;TERNI&quot;},{&quot;value&quot;:&quot;TS&quot;,&quot;label&quot;:&quot;TRIESTE&quot;},{&quot;value&quot;:&quot;TV&quot;,&quot;label&quot;:&quot;TREVISO&quot;},{&quot;value&quot;:&quot;UD&quot;,&quot;label&quot;:&quot;UDINE&quot;},{&quot;value&quot;:&quot;VA&quot;,&quot;label&quot;:&quot;VARESE&quot;},{&quot;value&quot;:&quot;VB&quot;,&quot;label&quot;:&quot;VERBANIA&quot;},{&quot;value&quot;:&quot;VC&quot;,&quot;label&quot;:&quot;VERCELLI&quot;},{&quot;value&quot;:&quot;VE&quot;,&quot;label&quot;:&quot;VENEZIA&quot;},{&quot;value&quot;:&quot;VI&quot;,&quot;label&quot;:&quot;VICENZA&quot;},{&quot;value&quot;:&quot;VR&quot;,&quot;label&quot;:&quot;VERONA&quot;},{&quot;value&quot;:&quot;VT&quot;,&quot;label&quot;:&quot;VITERBO&quot;},{&quot;value&quot;:&quot;VV&quot;,&quot;label&quot;:&quot;VIBO VALENTIA&quot;}],&quot;FlagRequiredInd&quot;:false,&quot;Compagnia&quot;:&quot;unipolsai&quot;,&quot;ResidenzaFiscaleStatoEstero&quot;:false,&quot;TipologiaSocietaFatca&quot;:&quot;0&quot;,&quot;FlagSedeLegale&quot;:true,&quot;ResidenzaFiscaleUsa&quot;:false,&quot;Options&quot;:[{&quot;value&quot;:&quot;Z200&quot;,&quot;label&quot;:&quot;AFGHANISTAN&quot;},{&quot;value&quot;:&quot;Z100&quot;,&quot;label&quot;:&quot;ALBANIA&quot;},{&quot;value&quot;:&quot;Z301&quot;,&quot;label&quot;:&quot;ALGERIA&quot;},{&quot;value&quot;:&quot;Z725&quot;,&quot;label&quot;:&quot;AMERICAN SAMOA&quot;},{&quot;value&quot;:&quot;Z101&quot;,&quot;label&quot;:&quot;ANDORRA&quot;},{&quot;value&quot;:&quot;Z302&quot;,&quot;label&quot;:&quot;ANGOLA&quot;},{&quot;value&quot;:&quot;Z529&quot;,&quot;label&quot;:&quot;ANGUILLA&quot;},{&quot;value&quot;:&quot;Z532&quot;,&quot;label&quot;:&quot;ANTIGUA E BARBUDA&quot;},{&quot;value&quot;:&quot;Z203&quot;,&quot;label&quot;:&quot;ARABIA SAUDITA&quot;},{&quot;value&quot;:&quot;Z600&quot;,&quot;label&quot;:&quot;ARGENTINA&quot;},{&quot;value&quot;:&quot;Z252&quot;,&quot;label&quot;:&quot;ARMENIA&quot;},{&quot;value&quot;:&quot;Z501&quot;,&quot;label&quot;:&quot;ARUBA&quot;},{&quot;value&quot;:&quot;Z700&quot;,&quot;label&quot;:&quot;AUSTRALIA&quot;},{&quot;value&quot;:&quot;Z102&quot;,&quot;label&quot;:&quot;AUSTRIA&quot;},{&quot;value&quot;:&quot;Z253&quot;,&quot;label&quot;:&quot;AZERBAIJAN&quot;},{&quot;value&quot;:&quot;Z502&quot;,&quot;label&quot;:&quot;BAHAMAS&quot;},{&quot;value&quot;:&quot;Z204&quot;,&quot;label&quot;:&quot;BAHREIN&quot;},{&quot;value&quot;:&quot;Z249&quot;,&quot;label&quot;:&quot;BANGLADESH&quot;},{&quot;value&quot;:&quot;Z522&quot;,&quot;label&quot;:&quot;BARBADOS&quot;},{&quot;value&quot;:&quot;Z103&quot;,&quot;label&quot;:&quot;BELGIO&quot;},{&quot;value&quot;:&quot;Z512&quot;,&quot;label&quot;:&quot;BELIZE&quot;},{&quot;value&quot;:&quot;Z314&quot;,&quot;label&quot;:&quot;BENIN&quot;},{&quot;value&quot;:&quot;Z400&quot;,&quot;label&quot;:&quot;BERMUDA&quot;},{&quot;value&quot;:&quot;Z205&quot;,&quot;label&quot;:&quot;BHUTAN&quot;},{&quot;value&quot;:&quot;Z139&quot;,&quot;label&quot;:&quot;BIELORUSSIA&quot;},{&quot;value&quot;:&quot;Z601&quot;,&quot;label&quot;:&quot;BOLIVIA&quot;},{&quot;value&quot;:&quot;Z153&quot;,&quot;label&quot;:&quot;BOSNIA ED ERZEGOVINA&quot;},{&quot;value&quot;:&quot;Z358&quot;,&quot;label&quot;:&quot;BOTSWANA&quot;},{&quot;value&quot;:&quot;Z602&quot;,&quot;label&quot;:&quot;BRASILE&quot;},{&quot;value&quot;:&quot;Z104&quot;,&quot;label&quot;:&quot;BULGARIA&quot;},{&quot;value&quot;:&quot;Z354&quot;,&quot;label&quot;:&quot;BURKINA FASO&quot;},{&quot;value&quot;:&quot;Z305&quot;,&quot;label&quot;:&quot;BURUNDI&quot;},{&quot;value&quot;:&quot;Z716&quot;,&quot;label&quot;:&quot;CALEDONIA NUOVA&quot;},{&quot;value&quot;:&quot;Z208&quot;,&quot;label&quot;:&quot;CAMBOGIA&quot;},{&quot;value&quot;:&quot;Z306&quot;,&quot;label&quot;:&quot;CAMERUN&quot;},{&quot;value&quot;:&quot;Z401&quot;,&quot;label&quot;:&quot;CANADA&quot;},{&quot;value&quot;:&quot;Z307&quot;,&quot;label&quot;:&quot;CAPO VERDE&quot;},{&quot;value&quot;:&quot;Z309&quot;,&quot;label&quot;:&quot;CIAD&quot;},{&quot;value&quot;:&quot;Z603&quot;,&quot;label&quot;:&quot;CILE&quot;},{&quot;value&quot;:&quot;Z210&quot;,&quot;label&quot;:&quot;CINA REPUBBLICA POPOLARE&quot;},{&quot;value&quot;:&quot;Z211&quot;,&quot;label&quot;:&quot;CIPRO&quot;},{&quot;value&quot;:&quot;Z106&quot;,&quot;label&quot;:&quot;CITTA&apos; DEL VATICANO&quot;},{&quot;value&quot;:&quot;Z604&quot;,&quot;label&quot;:&quot;COLOMBIA&quot;},{&quot;value&quot;:&quot;Z310&quot;,&quot;label&quot;:&quot;COMORE&quot;},{&quot;value&quot;:&quot;Z311&quot;,&quot;label&quot;:&quot;CONGO REPUBBLICA&quot;},{&quot;value&quot;:&quot;Z312&quot;,&quot;label&quot;:&quot;CONGO REPUBBLICA DEMOCRATICA&quot;},{&quot;value&quot;:&quot;Z214&quot;,&quot;label&quot;:&quot;COREA DEL NORD&quot;},{&quot;value&quot;:&quot;Z213&quot;,&quot;label&quot;:&quot;COREA DEL SUD&quot;},{&quot;value&quot;:&quot;Z313&quot;,&quot;label&quot;:&quot;COSTA D&apos;AVORIO&quot;},{&quot;value&quot;:&quot;Z503&quot;,&quot;label&quot;:&quot;COSTA RICA&quot;},{&quot;value&quot;:&quot;Z149&quot;,&quot;label&quot;:&quot;CROAZIA&quot;},{&quot;value&quot;:&quot;Z504&quot;,&quot;label&quot;:&quot;CUBA&quot;},{&quot;value&quot;:&quot;Z107&quot;,&quot;label&quot;:&quot;DANIMARCA&quot;},{&quot;value&quot;:&quot;Z900&quot;,&quot;label&quot;:&quot;DIPENDENZE AUSTRALIANE&quot;},{&quot;value&quot;:&quot;Z901&quot;,&quot;label&quot;:&quot;DIPENDENZE BRITANNICHE&quot;},{&quot;value&quot;:&quot;Z800&quot;,&quot;label&quot;:&quot;DIPENDENZE CANADESI&quot;},{&quot;value&quot;:&quot;Z902&quot;,&quot;label&quot;:&quot;DIPENDENZE FRANCESI&quot;},{&quot;value&quot;:&quot;Z903&quot;,&quot;label&quot;:&quot;DIPENDENZE NEOZELANDESI&quot;},{&quot;value&quot;:&quot;Z904&quot;,&quot;label&quot;:&quot;DIPENDENZE NORVEGESI ANTARTICHE&quot;},{&quot;value&quot;:&quot;Z801&quot;,&quot;label&quot;:&quot;DIPENDENZE NORVEGESI ARTICHE&quot;},{&quot;value&quot;:&quot;Z802&quot;,&quot;label&quot;:&quot;DIPENDENZE RUSSE&quot;},{&quot;value&quot;:&quot;Z905&quot;,&quot;label&quot;:&quot;DIPENDENZE STATUNITENSI&quot;},{&quot;value&quot;:&quot;Z906&quot;,&quot;label&quot;:&quot;DIPENDENZE SUDAFRICANE&quot;},{&quot;value&quot;:&quot;Z361&quot;,&quot;label&quot;:&quot;DJIBOUTI&quot;},{&quot;value&quot;:&quot;Z526&quot;,&quot;label&quot;:&quot;DOMINICA&quot;},{&quot;value&quot;:&quot;Z605&quot;,&quot;label&quot;:&quot;ECUADOR&quot;},{&quot;value&quot;:&quot;Z336&quot;,&quot;label&quot;:&quot;EGITTO&quot;},{&quot;value&quot;:&quot;Z506&quot;,&quot;label&quot;:&quot;EL SALVADOR&quot;},{&quot;value&quot;:&quot;Z215&quot;,&quot;label&quot;:&quot;EMIRATI ARABI UNITI&quot;},{&quot;value&quot;:&quot;Z215&quot;,&quot;label&quot;:&quot;EMIRATI ARABI UNITI&quot;},{&quot;value&quot;:&quot;Z215&quot;,&quot;label&quot;:&quot;EMIRATI ARABI UNITI&quot;},{&quot;value&quot;:&quot;Z215&quot;,&quot;label&quot;:&quot;EMIRATI ARABI UNITI&quot;},{&quot;value&quot;:&quot;Z215&quot;,&quot;label&quot;:&quot;EMIRATI ARABI UNITI&quot;},{&quot;value&quot;:&quot;Z215&quot;,&quot;label&quot;:&quot;EMIRATI ARABI UNITI&quot;},{&quot;value&quot;:&quot;Z215&quot;,&quot;label&quot;:&quot;EMIRATI ARABI UNITI&quot;},{&quot;value&quot;:&quot;Z215&quot;,&quot;label&quot;:&quot;EMIRATI ARABI UNITI&quot;},{&quot;value&quot;:&quot;Z368&quot;,&quot;label&quot;:&quot;ERITREA&quot;},{&quot;value&quot;:&quot;Z144&quot;,&quot;label&quot;:&quot;ESTONIA&quot;},{&quot;value&quot;:&quot;Z349&quot;,&quot;label&quot;:&quot;ESWATINI&quot;},{&quot;value&quot;:&quot;Z315&quot;,&quot;label&quot;:&quot;ETIOPIA&quot;},{&quot;value&quot;:&quot;Z108&quot;,&quot;label&quot;:&quot;FAROE ISLANDS&quot;},{&quot;value&quot;:&quot;Z704&quot;,&quot;label&quot;:&quot;FIJI&quot;},{&quot;value&quot;:&quot;Z216&quot;,&quot;label&quot;:&quot;FILIPPINE&quot;},{&quot;value&quot;:&quot;Z109&quot;,&quot;label&quot;:&quot;FINLANDIA&quot;},{&quot;value&quot;:&quot;Z110&quot;,&quot;label&quot;:&quot;FRANCIA&quot;},{&quot;value&quot;:&quot;Z316&quot;,&quot;label&quot;:&quot;GABON&quot;},{&quot;value&quot;:&quot;Z317&quot;,&quot;label&quot;:&quot;GAMBIA&quot;},{&quot;value&quot;:&quot;Z254&quot;,&quot;label&quot;:&quot;GEORGIA&quot;},{&quot;value&quot;:&quot;Z112&quot;,&quot;label&quot;:&quot;GERMANIA&quot;},{&quot;value&quot;:&quot;Z260&quot;,&quot;label&quot;:&quot;GERUSALEMME&quot;},{&quot;value&quot;:&quot;Z318&quot;,&quot;label&quot;:&quot;GHANA&quot;},{&quot;value&quot;:&quot;Z507&quot;,&quot;label&quot;:&quot;GIAMAICA&quot;},{&quot;value&quot;:&quot;Z219&quot;,&quot;label&quot;:&quot;GIAPPONE&quot;},{&quot;value&quot;:&quot;Z113&quot;,&quot;label&quot;:&quot;GIBILTERRA&quot;},{&quot;value&quot;:&quot;Z220&quot;,&quot;label&quot;:&quot;GIORDANIA&quot;},{&quot;value&quot;:&quot;Z115&quot;,&quot;label&quot;:&quot;GRECIA&quot;},{&quot;value&quot;:&quot;Z524&quot;,&quot;label&quot;:&quot;GRENADA&quot;},{&quot;value&quot;:&quot;Z402&quot;,&quot;label&quot;:&quot;GROENLANDIA&quot;},{&quot;value&quot;:&quot;Z508&quot;,&quot;label&quot;:&quot;GUADALUPE&quot;},{&quot;value&quot;:&quot;Z706&quot;,&quot;label&quot;:&quot;GUAM&quot;},{&quot;value&quot;:&quot;Z509&quot;,&quot;label&quot;:&quot;GUATEMALA&quot;},{&quot;value&quot;:&quot;Z319&quot;,&quot;label&quot;:&quot;GUINEA&quot;},{&quot;value&quot;:&quot;Z321&quot;,&quot;label&quot;:&quot;GUINEA EQUATORIALE&quot;},{&quot;value&quot;:&quot;Z320&quot;,&quot;label&quot;:&quot;GUINEA-BISSAU&quot;},{&quot;value&quot;:&quot;Z606&quot;,&quot;label&quot;:&quot;GUYANA&quot;},{&quot;value&quot;:&quot;Z607&quot;,&quot;label&quot;:&quot;GUYANA FRANCESE&quot;},{&quot;value&quot;:&quot;Z510&quot;,&quot;label&quot;:&quot;HAITI&quot;},{&quot;value&quot;:&quot;Z511&quot;,&quot;label&quot;:&quot;HONDURAS&quot;},{&quot;value&quot;:&quot;Z222&quot;,&quot;label&quot;:&quot;INDIA&quot;},{&quot;value&quot;:&quot;Z223&quot;,&quot;label&quot;:&quot;INDONESIA&quot;},{&quot;value&quot;:&quot;Z224&quot;,&quot;label&quot;:&quot;IRAN&quot;},{&quot;value&quot;:&quot;Z225&quot;,&quot;label&quot;:&quot;IRAQ&quot;},{&quot;value&quot;:&quot;Z707&quot;,&quot;label&quot;:&quot;IRIAN OCCIDENTALE&quot;},{&quot;value&quot;:&quot;Z116&quot;,&quot;label&quot;:&quot;IRLANDA&quot;},{&quot;value&quot;:&quot;Z117&quot;,&quot;label&quot;:&quot;ISLANDA&quot;},{&quot;value&quot;:&quot;Z702&quot;,&quot;label&quot;:&quot;ISOLA CHRISTMAS&quot;},{&quot;value&quot;:&quot;Z715&quot;,&quot;label&quot;:&quot;ISOLA NORFOLK&quot;},{&quot;value&quot;:&quot;Z530&quot;,&quot;label&quot;:&quot;ISOLE CAYMAN&quot;},{&quot;value&quot;:&quot;Z212&quot;,&quot;label&quot;:&quot;ISOLE COCOS&quot;},{&quot;value&quot;:&quot;Z703&quot;,&quot;label&quot;:&quot;ISOLE COOK&quot;},{&quot;value&quot;:&quot;Z609&quot;,&quot;label&quot;:&quot;ISOLE FALKLAND&quot;},{&quot;value&quot;:&quot;Z711&quot;,&quot;label&quot;:&quot;ISOLE MARSHALL&quot;},{&quot;value&quot;:&quot;Z724&quot;,&quot;label&quot;:&quot;ISOLE SOLOMON&quot;},{&quot;value&quot;:&quot;Z519&quot;,&quot;label&quot;:&quot;ISOLE TURKS E CAICOS&quot;},{&quot;value&quot;:&quot;Z520&quot;,&quot;label&quot;:&quot;ISOLE VERGINI AMERICANE&quot;},{&quot;value&quot;:&quot;Z525&quot;,&quot;label&quot;:&quot;ISOLE VERGINI BRITANNICHE&quot;},{&quot;value&quot;:&quot;Z729&quot;,&quot;label&quot;:&quot;ISOLE WALLIS E FUTUNA&quot;},{&quot;value&quot;:&quot;Z226&quot;,&quot;label&quot;:&quot;ISRAELE&quot;},{&quot;value&quot;:&quot;Z000&quot;,&quot;label&quot;:&quot;ITALIA&quot;},{&quot;value&quot;:&quot;Z124&quot;,&quot;label&quot;:&quot;JERSEY (BALIATO DI)&quot;},{&quot;value&quot;:&quot;Z255&quot;,&quot;label&quot;:&quot;KAZAKHSTAN&quot;},{&quot;value&quot;:&quot;Z322&quot;,&quot;label&quot;:&quot;KENYA&quot;},{&quot;value&quot;:&quot;Z731&quot;,&quot;label&quot;:&quot;KIRIBATI&quot;},{&quot;value&quot;:&quot;Z160&quot;,&quot;label&quot;:&quot;KOSOVO&quot;},{&quot;value&quot;:&quot;Z227&quot;,&quot;label&quot;:&quot;KUWAIT&quot;},{&quot;value&quot;:&quot;Z256&quot;,&quot;label&quot;:&quot;KYRGYZSTAN&quot;},{&quot;value&quot;:&quot;Z228&quot;,&quot;label&quot;:&quot;LAO PEOPLE&apos;S DEMOCRATIC REPUBLIC&quot;},{&quot;value&quot;:&quot;Z359&quot;,&quot;label&quot;:&quot;LESOTHO&quot;},{&quot;value&quot;:&quot;Z145&quot;,&quot;label&quot;:&quot;LETTONIA&quot;},{&quot;value&quot;:&quot;Z229&quot;,&quot;label&quot;:&quot;LIBANO&quot;},{&quot;value&quot;:&quot;Z325&quot;,&quot;label&quot;:&quot;LIBERIA&quot;},{&quot;value&quot;:&quot;Z326&quot;,&quot;label&quot;:&quot;LIBIA&quot;},{&quot;value&quot;:&quot;Z119&quot;,&quot;label&quot;:&quot;LIECHTENSTEIN&quot;},{&quot;value&quot;:&quot;Z146&quot;,&quot;label&quot;:&quot;LITUANIA&quot;},{&quot;value&quot;:&quot;Z120&quot;,&quot;label&quot;:&quot;LUSSEMBURGO&quot;},{&quot;value&quot;:&quot;Z231&quot;,&quot;label&quot;:&quot;MACAU&quot;},{&quot;value&quot;:&quot;Z148&quot;,&quot;label&quot;:&quot;MACEDONIA&quot;},{&quot;value&quot;:&quot;Z327&quot;,&quot;label&quot;:&quot;MADAGASCAR&quot;},{&quot;value&quot;:&quot;Z328&quot;,&quot;label&quot;:&quot;MALAWI&quot;},{&quot;value&quot;:&quot;Z247&quot;,&quot;label&quot;:&quot;MALAYSIA&quot;},{&quot;value&quot;:&quot;Z232&quot;,&quot;label&quot;:&quot;MALDIVE&quot;},{&quot;value&quot;:&quot;Z329&quot;,&quot;label&quot;:&quot;MALI&quot;},{&quot;value&quot;:&quot;Z121&quot;,&quot;label&quot;:&quot;MALTA&quot;},{&quot;value&quot;:&quot;Z122&quot;,&quot;label&quot;:&quot;MAN&quot;},{&quot;value&quot;:&quot;Z710&quot;,&quot;label&quot;:&quot;MARIANNE DEL NORD&quot;},{&quot;value&quot;:&quot;Z330&quot;,&quot;label&quot;:&quot;MAROCCO&quot;},{&quot;value&quot;:&quot;Z513&quot;,&quot;label&quot;:&quot;MARTINICA&quot;},{&quot;value&quot;:&quot;Z331&quot;,&quot;label&quot;:&quot;MAURITANIA&quot;},{&quot;value&quot;:&quot;Z332&quot;,&quot;label&quot;:&quot;MAURITIUS&quot;},{&quot;value&quot;:&quot;Z360&quot;,&quot;label&quot;:&quot;MAYOTTE&quot;},{&quot;value&quot;:&quot;Z514&quot;,&quot;label&quot;:&quot;MESSICO&quot;},{&quot;value&quot;:&quot;Z735&quot;,&quot;label&quot;:&quot;MICRONESIA&quot;},{&quot;value&quot;:&quot;Z140&quot;,&quot;label&quot;:&quot;MOLDAVIA&quot;},{&quot;value&quot;:&quot;Z123&quot;,&quot;label&quot;:&quot;MONACO&quot;},{&quot;value&quot;:&quot;Z233&quot;,&quot;label&quot;:&quot;MONGOLIA&quot;},{&quot;value&quot;:&quot;Z159&quot;,&quot;label&quot;:&quot;MONTENEGRO&quot;},{&quot;value&quot;:&quot;Z531&quot;,&quot;label&quot;:&quot;MONTSERRAT&quot;},{&quot;value&quot;:&quot;Z333&quot;,&quot;label&quot;:&quot;MOZAMBICO&quot;},{&quot;value&quot;:&quot;Z206&quot;,&quot;label&quot;:&quot;MYANMAR&quot;},{&quot;value&quot;:&quot;Z300&quot;,&quot;label&quot;:&quot;NAMIBIA&quot;},{&quot;value&quot;:&quot;Z713&quot;,&quot;label&quot;:&quot;NAURU&quot;},{&quot;value&quot;:&quot;Z234&quot;,&quot;label&quot;:&quot;NEPAL&quot;},{&quot;value&quot;:&quot;Z515&quot;,&quot;label&quot;:&quot;NICARAGUA&quot;},{&quot;value&quot;:&quot;Z334&quot;,&quot;label&quot;:&quot;NIGER&quot;},{&quot;value&quot;:&quot;Z335&quot;,&quot;label&quot;:&quot;NIGERIA&quot;},{&quot;value&quot;:&quot;Z714&quot;,&quot;label&quot;:&quot;NIUE&quot;},{&quot;value&quot;:&quot;Z125&quot;,&quot;label&quot;:&quot;NORVEGIA&quot;},{&quot;value&quot;:&quot;Z719&quot;,&quot;label&quot;:&quot;NUOVA ZELANDA&quot;},{&quot;value&quot;:&quot;Z235&quot;,&quot;label&quot;:&quot;OMAN&quot;},{&quot;value&quot;:&quot;Z126&quot;,&quot;label&quot;:&quot;PAESI BASSI&quot;},{&quot;value&quot;:&quot;Z236&quot;,&quot;label&quot;:&quot;PAKISTAN&quot;},{&quot;value&quot;:&quot;Z734&quot;,&quot;label&quot;:&quot;PALAU REPUBBLICA&quot;},{&quot;value&quot;:&quot;Z516&quot;,&quot;label&quot;:&quot;PANAMA&quot;},{&quot;value&quot;:&quot;Z730&quot;,&quot;label&quot;:&quot;PAPUA NUOVA GUINEA&quot;},{&quot;value&quot;:&quot;Z610&quot;,&quot;label&quot;:&quot;PARAGUAY&quot;},{&quot;value&quot;:&quot;Z611&quot;,&quot;label&quot;:&quot;PERU&apos;&quot;},{&quot;value&quot;:&quot;Z722&quot;,&quot;label&quot;:&quot;PITCAIRN&quot;},{&quot;value&quot;:&quot;Z723&quot;,&quot;label&quot;:&quot;POLINESIA FRANCESE&quot;},{&quot;value&quot;:&quot;Z127&quot;,&quot;label&quot;:&quot;POLONIA&quot;},{&quot;value&quot;:&quot;Z128&quot;,&quot;label&quot;:&quot;PORTOGALLO&quot;},{&quot;value&quot;:&quot;Z518&quot;,&quot;label&quot;:&quot;PUERTO RICO&quot;},{&quot;value&quot;:&quot;Z237&quot;,&quot;label&quot;:&quot;QATAR&quot;},{&quot;value&quot;:&quot;Z114&quot;,&quot;label&quot;:&quot;REGNO UNITO&quot;},{&quot;value&quot;:&quot;Z156&quot;,&quot;label&quot;:&quot;REPUBBLICA CECA&quot;},{&quot;value&quot;:&quot;Z308&quot;,&quot;label&quot;:&quot;REPUBBLICA CENTRAFRICANA&quot;},{&quot;value&quot;:&quot;Z907&quot;,&quot;label&quot;:&quot;REPUBBLICA DEL SUD SUDAN&quot;},{&quot;value&quot;:&quot;Z505&quot;,&quot;label&quot;:&quot;REPUBBLICA DOMINICANA&quot;},{&quot;value&quot;:&quot;Z324&quot;,&quot;label&quot;:&quot;REUNION&quot;},{&quot;value&quot;:&quot;Z129&quot;,&quot;label&quot;:&quot;ROMANIA&quot;},{&quot;value&quot;:&quot;Z338&quot;,&quot;label&quot;:&quot;RUANDA&quot;},{&quot;value&quot;:&quot;Z154&quot;,&quot;label&quot;:&quot;RUSSIA&quot;},{&quot;value&quot;:&quot;Z339&quot;,&quot;label&quot;:&quot;SAHARA OCCIDENTALE&quot;},{&quot;value&quot;:&quot;Z533&quot;,&quot;label&quot;:&quot;SAINT CHRISTOPHER E NEVIS&quot;},{&quot;value&quot;:&quot;Z527&quot;,&quot;label&quot;:&quot;SAINT LUCIA&quot;},{&quot;value&quot;:&quot;Z528&quot;,&quot;label&quot;:&quot;SAINT VINCENT E GRANADINE&quot;},{&quot;value&quot;:&quot;Z726&quot;,&quot;label&quot;:&quot;SAMOA&quot;},{&quot;value&quot;:&quot;Z130&quot;,&quot;label&quot;:&quot;SAN MARINO&quot;},{&quot;value&quot;:&quot;Z340&quot;,&quot;label&quot;:&quot;SANT&apos;ELENA&quot;},{&quot;value&quot;:&quot;Z341&quot;,&quot;label&quot;:&quot;SAO TOME&apos; E PRINCIPE&quot;},{&quot;value&quot;:&quot;Z343&quot;,&quot;label&quot;:&quot;SENEGAL&quot;},{&quot;value&quot;:&quot;Z158&quot;,&quot;label&quot;:&quot;SERBIA&quot;},{&quot;value&quot;:&quot;Z342&quot;,&quot;label&quot;:&quot;SEYCHELLES&quot;},{&quot;value&quot;:&quot;Z344&quot;,&quot;label&quot;:&quot;SIERRA LEONE&quot;},{&quot;value&quot;:&quot;Z248&quot;,&quot;label&quot;:&quot;SINGAPORE&quot;},{&quot;value&quot;:&quot;Z240&quot;,&quot;label&quot;:&quot;SIRIA&quot;},{&quot;value&quot;:&quot;Z155&quot;,&quot;label&quot;:&quot;SLOVACCHIA&quot;},{&quot;value&quot;:&quot;Z150&quot;,&quot;label&quot;:&quot;SLOVENIA&quot;},{&quot;value&quot;:&quot;Z345&quot;,&quot;label&quot;:&quot;SOMALIA&quot;},{&quot;value&quot;:&quot;Z131&quot;,&quot;label&quot;:&quot;SPAGNA&quot;},{&quot;value&quot;:&quot;Z209&quot;,&quot;label&quot;:&quot;SRI LANKA&quot;},{&quot;value&quot;:&quot;Z403&quot;,&quot;label&quot;:&quot;ST. PIERRE AND MIQUELON&quot;},{&quot;value&quot;:&quot;Z404&quot;,&quot;label&quot;:&quot;STATI UNITI D&apos;AMERICA&quot;},{&quot;value&quot;:&quot;Z347&quot;,&quot;label&quot;:&quot;SUD AFRICA&quot;},{&quot;value&quot;:&quot;Z348&quot;,&quot;label&quot;:&quot;SUDAN&quot;},{&quot;value&quot;:&quot;Z207&quot;,&quot;label&quot;:&quot;SULTANATO DEL BRUNEI&quot;},{&quot;value&quot;:&quot;Z608&quot;,&quot;label&quot;:&quot;SURINAME&quot;},{&quot;value&quot;:&quot;Z132&quot;,&quot;label&quot;:&quot;SVEZIA&quot;},{&quot;value&quot;:&quot;Z133&quot;,&quot;label&quot;:&quot;SVIZZERA&quot;},{&quot;value&quot;:&quot;Z257&quot;,&quot;label&quot;:&quot;TAGIKISTAN&quot;},{&quot;value&quot;:&quot;Z217&quot;,&quot;label&quot;:&quot;TAIWAN&quot;},{&quot;value&quot;:&quot;Z357&quot;,&quot;label&quot;:&quot;TANZANIA&quot;},{&quot;value&quot;:&quot;Z161&quot;,&quot;label&quot;:&quot;TERRITORI PALESTINESI&quot;},{&quot;value&quot;:&quot;Z241&quot;,&quot;label&quot;:&quot;THAILANDIA&quot;},{&quot;value&quot;:&quot;Z242&quot;,&quot;label&quot;:&quot;TIMOR-LESTE&quot;},{&quot;value&quot;:&quot;Z351&quot;,&quot;label&quot;:&quot;TOGO&quot;},{&quot;value&quot;:&quot;Z727&quot;,&quot;label&quot;:&quot;TOKELAU&quot;},{&quot;value&quot;:&quot;Z728&quot;,&quot;label&quot;:&quot;TONGA&quot;},{&quot;value&quot;:&quot;Z612&quot;,&quot;label&quot;:&quot;TRINIDAD E TOBAGO&quot;},{&quot;value&quot;:&quot;Z352&quot;,&quot;label&quot;:&quot;TUNISIA&quot;},{&quot;value&quot;:&quot;Z243&quot;,&quot;label&quot;:&quot;TURCHIA&quot;},{&quot;value&quot;:&quot;Z258&quot;,&quot;label&quot;:&quot;TURKMENISTAN&quot;},{&quot;value&quot;:&quot;Z732&quot;,&quot;label&quot;:&quot;TUVALU&quot;},{&quot;value&quot;:&quot;Z138&quot;,&quot;label&quot;:&quot;UCRAINA&quot;},{&quot;value&quot;:&quot;Z353&quot;,&quot;label&quot;:&quot;UGANDA&quot;},{&quot;value&quot;:&quot;Z134&quot;,&quot;label&quot;:&quot;UNGHERIA&quot;},{&quot;value&quot;:&quot;Z613&quot;,&quot;label&quot;:&quot;URUGUAY&quot;},{&quot;value&quot;:&quot;Z259&quot;,&quot;label&quot;:&quot;UZBEKISTAN&quot;},{&quot;value&quot;:&quot;Z733&quot;,&quot;label&quot;:&quot;VANUATU&quot;},{&quot;value&quot;:&quot;Z614&quot;,&quot;label&quot;:&quot;VENEZUELA&quot;},{&quot;value&quot;:&quot;Z251&quot;,&quot;label&quot;:&quot;VIETNAM&quot;},{&quot;value&quot;:&quot;Z246&quot;,&quot;label&quot;:&quot;YEMEN&quot;},{&quot;value&quot;:&quot;Z355&quot;,&quot;label&quot;:&quot;ZAMBIA&quot;},{&quot;value&quot;:&quot;Z337&quot;,&quot;label&quot;:&quot;ZIMBABWE&quot;}]}</sampleDataSourceResponse>
    <versionNumber>3</versionNumber>
</OmniUiCard>
