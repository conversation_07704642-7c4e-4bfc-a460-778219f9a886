import { LightningElement, track } from 'lwc';
import getUserNetworkData from '@salesforce/apex/NetworkUserHelper.getUserNetworkData';
import setPreferredNetworkUser from '@salesforce/apex/NetworkUserHelper.setPreferredNetworkUser';
import FEDERATION_ID_WARNING from '@salesforce/label/c.FederationIdWarningMsg';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';

export default class SetDefaultUser extends LightningElement {
    @track utenze = [];
    @track originalUtenze = [];
    @track loading = false;
    @track showFederationWarning = false;
    @track federationWarningMsg = FEDERATION_ID_WARNING; // Variabile dinamica

    get federationWarningMsg() {
        return this.federationWarningMsg;
    }

    connectedCallback() {
        this.loadData();
    }

    loadData() {
        this.loading = true;
        this.showFederationWarning = false;
        console.log('@@DEBUG: Start caricamento utenze');
        getUserNetworkData()
            .then(data => {
                console.log('@@DEBUG data: ', data);
                if (!data || data.length === 0) {
                    // FederationIdentifier is blank or no utenze found
                    this.showFederationWarning = true;
                    this.utenze = [];
                    this.loading = false;
                    return;
                }
                this.utenze = data.map(item => ({
                    compagnia: item.companyName,
                    companyId: item.companyId,
                    options: item.options,
                    selected: item.selectedUserId
                }));
                this.originalUtenze = JSON.parse(JSON.stringify(this.utenze));
                this.loading = false;
            })
            .catch(error => {
                this.loading = false;
                if (error && error.body && error.body.message) {
                    this.federationWarningMsg = "ERRORE CRUSCOTTO: " + error.body.message;
                } else {
                    this.federationWarningMsg = FEDERATION_ID_WARNING;
                }
                this.showFederationWarning = true;
                this.utenze = [];
            });
    }

    handleChange(event) {
        const companyId = event.target.name;
        const selectedUserId = event.detail.value;
        this.utenze = this.utenze.map(u => 
            u.companyId === companyId ? { ...u, selected: selectedUserId } : u
        );
    }

    handleConferma() {
        this.loading = true;
        const promises = this.utenze.map(u => {
            if (u.selected) {
                return setPreferredNetworkUser({ networkUserId: u.selected, companyId: u.companyId });
            }
            return Promise.resolve();
        });
        Promise.all(promises)
            .then(() => {
                this.loading = false;
                // Mostra il toast di successo
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: 'Successo',
                        message: 'Salvataggio completato con successo!',
                        variant: 'success'
                    })
                );
                // Attendi un secondo prima di ricaricare la pagina
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            })
            .catch(error => {
                this.loading = false;
                // Mostra il toast di errore
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: 'Errore',
                        message: 'Si è verificato un errore durante il salvataggio.',
                        variant: 'error'
                    })
                );
            });
    }

    handleAnnulla() {
        this.utenze = JSON.parse(JSON.stringify(this.originalUtenze));
    }
}