import { LightningElement, api, track, wire } from 'lwc';
import { OmniscriptBaseMixin } from 'omnistudio/omniscriptBaseMixin';
import { CurrentPageReference } from 'lightning/navigation';
import { NavigationMixin } from 'lightning/navigation';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';

export default class TitoliInScandenzaLegale extends OmniscriptBaseMixin(LightningElement) {
  @track titoliInScadenzaData = []
  @track isLoading = true;
  @track showTable = false;
  isUnipol = false;
  isUniSalute = false;
  accountId;
  userId;
  @track societa = [];
  

  columnsUnipol = [
    { label: 'Società', fieldName: 'societa' },
    { label: 'Tipo', fieldName: 'tipo' },
    { label: 'Ambito', fieldName: 'ambito' },
    { label: 'N. Folder', fieldName: 'nFolder' },
    { label: 'N. Polizza/Posizione', fieldName: 'nPolizzaPosizione' },
    { label: 'Scadenza', fieldName: 'scadenza' },
    { label: 'Frazionamento', fieldName: 'frazionamento' },
    { label: 'Premio (comprensivo di Unibox)', fieldName: 'premio' },
    { label: 'di cui Unibox', fieldName: 'unibox' },
    { label: 'Omnicanalità', fieldName: 'omnicanalita' },
    { label: 'Esposizione AR', fieldName: 'esposizioneAR' },
    { label: 'Targa', fieldName: 'targa' },
    { label: 'Modello', fieldName: 'modello' },
    { label: 'Titoli al legale', fieldName: 'titoliLegale' },
    { label: 'Rettificabile', fieldName: 'rettificabile' },
    { label: 'Firmata', fieldName: 'firmata' },
  ];


  @wire(CurrentPageReference)
  getStateParameters(currentPageReference) {
    if (currentPageReference?.state) {
      try {
        this.accountId = currentPageReference.state.c__AccountId;
        this.userId = currentPageReference.state.c__UserId;
        console.log('accountId:', this.accountId);
        console.log('userId:', this.userId);
        if (this.accountId) {
          this.callIntegrationProcedure();
        }
      } catch (e) {
        console.error('Errore parsing accountId:', e);
      }
    }
  }

  callIntegrationProcedure() {
    if (!this.accountId) {
      console.error('AccountId non disponibile, la chiamata non verrà eseguita.');
      return;
    }
    console.log('AccountId:', this.accountId);
    const inputParams = {
      AccountId: this.accountId,
      UserId: this.userId
    };
    console.log('Input IP:', JSON.stringify(inputParams, null, 2));
    const params = {
      input: JSON.stringify(inputParams),
      sClassName: 'omnistudio.IntegrationProcedureService',
      sMethodName: 'UniDS_Contenziosi',
      options: '{}'
    };

    this.omniRemoteCall(params, true)
      .then(response => {
        console.log('RESPONSE:', JSON.stringify(response, null, 2));
        let titoli;

        if (!response.error) {
          titoli = response.result.IPResult.Contenziosi;

          // Se IPResult non è un array, lo trasformo in un array
          if (!Array.isArray(titoli)) {
            titoli = [titoli];
          }
        }

        this.titoliInScadenzaData = titoli.map((item, index) => ({
          id: index,
          societa: item.compagnia,
          tipo: item.tipo,
          ambito: item.ambito,
          nFolder: item.nFolder,
          nPolizzaPosizione: item.ramo != undefined ? Array.isArray(item.polizza) ? item.ramo + '/' + item.polizza.join(', ') : item.ramo + '/' + item.polizza : item.polizza,
          scadenza: item.dataScadenzaTitolo,
          frazionamento: item.frazionamento,
          premio: item.premio,
          unibox: item.unibox,
          omnicanalita: item.omnicanalita,
          esposizioneAR: item.esposizioneAR,
          targa: item.targa,
          modello: item.modello,
          titoliLegale: item.titoliAlLegale,
          rettificabile: item.rettificabile,
          firmata: item.firmata,
          idContrattoPTF: item.idContrattoPTF
        }));

        this.isUnipol = titoli.some(item => item.isUnipol === true);
        this.isUniSalute = titoli.some(item => item.isUniSalute === true);
        this.isLoading = false;
        this.showTable = true;
      })
      .catch(error => {
        console.error('Errore nella chiamata Integration Procedure:', error);
        console.error('Params:', JSON.stringify(params, null, 2));
        this.isLoading = false;
        this.showTable = true;
      });
  }

}